"""
Resource management utilities for the DDB SharePoint RAG application.
Provides context managers and utilities for proper cleanup of files and HTTP sessions.
"""

import logging
import tempfile
import shutil
import asyncio
import aiohttp
from pathlib import Path
from typing import Optional, AsyncContextManager, Union
from contextlib import asynccontextmanager, contextmanager
import os
import time
from config import settings

logger = logging.getLogger(__name__)

class FileManager:
    """Context manager for temporary file and directory operations."""
    
    @staticmethod
    @contextmanager
    def temporary_file(suffix: str = "", prefix: str = "ddb_", delete: bool = True):
        """
        Context manager for temporary files with automatic cleanup.
        
        Args:
            suffix: File suffix/extension
            prefix: File prefix
            delete: Whether to delete file on exit (default: True)
            
        Yields:
            Path: Path to the temporary file
        """
        temp_file = None
        try:
            # Create temporary file
            fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
            os.close(fd)  # Close file descriptor immediately
            temp_file = Path(temp_path)
            
            logger.debug(f"Created temporary file: {temp_file}")
            yield temp_file
            
        except Exception as e:
            logger.error(f"Error in temporary file context: {e}")
            raise
        finally:
            # Cleanup
            if temp_file and temp_file.exists():
                try:
                    if delete:
                        temp_file.unlink()
                        logger.debug(f"Cleaned up temporary file: {temp_file}")
                    else:
                        logger.debug(f"Temporary file preserved: {temp_file}")
                except Exception as e:
                    logger.warning(f"Failed to cleanup temporary file {temp_file}: {e}")
    
    @staticmethod
    @contextmanager
    def temporary_directory(prefix: str = "ddb_", cleanup: bool = True):
        """
        Context manager for temporary directories with automatic cleanup.
        
        Args:
            prefix: Directory prefix
            cleanup: Whether to cleanup directory on exit (default: True)
            
        Yields:
            Path: Path to the temporary directory
        """
        temp_dir = None
        try:
            # Create temporary directory
            temp_dir = Path(tempfile.mkdtemp(prefix=prefix))
            logger.debug(f"Created temporary directory: {temp_dir}")
            yield temp_dir
            
        except Exception as e:
            logger.error(f"Error in temporary directory context: {e}")
            raise
        finally:
            # Cleanup
            if temp_dir and temp_dir.exists() and cleanup:
                try:
                    shutil.rmtree(temp_dir)
                    logger.debug(f"Cleaned up temporary directory: {temp_dir}")
                except Exception as e:
                    logger.warning(f"Failed to cleanup temporary directory {temp_dir}: {e}")
    
    @staticmethod
    @contextmanager
    def managed_path(file_path: Union[str, Path], cleanup_on_error: bool = True):
        """
        Context manager for file paths with optional cleanup on error.
        
        Args:
            file_path: Path to manage
            cleanup_on_error: Whether to cleanup file if an exception occurs
            
        Yields:
            Path: The managed file path
        """
        path = Path(file_path)
        try:
            yield path
        except Exception as e:
            if cleanup_on_error and path.exists():
                try:
                    path.unlink()
                    logger.debug(f"Cleaned up file after error: {path}")
                except Exception as cleanup_error:
                    logger.warning(f"Failed to cleanup file {path} after error: {cleanup_error}")
            raise


class HttpSessionManager:
    """
    HTTP session manager with connection pooling and proper resource cleanup.
    Provides reusable aiohttp sessions with automatic cleanup.
    """
    
    def __init__(self, max_sessions: int = 5, session_timeout: int = 300):
        """
        Initialize the session manager.
        
        Args:
            max_sessions: Maximum number of concurrent sessions
            session_timeout: Session timeout in seconds
        """
        self.max_sessions = max_sessions
        self.session_timeout = session_timeout
        self._sessions = []
        self._session_semaphore = asyncio.Semaphore(max_sessions)
        self._cleanup_tasks = set()
        
    @asynccontextmanager
    async def get_session(self, 
                         connector_limit: int = 100,
                         connector_limit_per_host: int = 30,
                         timeout: int = 30) -> AsyncContextManager[aiohttp.ClientSession]:
        """
        Get a managed HTTP session with automatic cleanup.
        
        Args:
            connector_limit: Total connection pool size
            connector_limit_per_host: Max connections per host
            timeout: Request timeout in seconds
            
        Yields:
            aiohttp.ClientSession: Configured HTTP session
        """
        session = None
        async with self._session_semaphore:
            try:
                # Create connector with connection limits
                connector = aiohttp.TCPConnector(
                    limit=connector_limit,
                    limit_per_host=connector_limit_per_host,
                    ttl_dns_cache=300,  # DNS cache TTL
                    use_dns_cache=True,
                )
                
                # Create session with timeout configuration
                timeout_config = aiohttp.ClientTimeout(total=timeout)
                
                session = aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout_config,
                )
                
                logger.debug("Created new HTTP session")
                yield session
                
            except Exception as e:
                logger.error(f"Error in HTTP session context: {e}")
                raise
            finally:
                if session and not session.closed:
                    try:
                        await session.close()
                        logger.debug("Closed HTTP session")
                    except Exception as e:
                        logger.warning(f"Error closing HTTP session: {e}")
    
    async def close_all_sessions(self):
        """Close all active sessions and cleanup resources."""
        cleanup_tasks = []
        
        for session in self._sessions:
            if not session.closed:
                cleanup_tasks.append(session.close())
        
        if cleanup_tasks:
            try:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
                logger.info(f"Closed {len(cleanup_tasks)} HTTP sessions")
            except Exception as e:
                logger.warning(f"Error during session cleanup: {e}")
        
        self._sessions.clear()


class ResourceContext:
    """
    Comprehensive resource management context that combines file and session management.
    Use this for operations that need both temporary files and HTTP requests.
    """
    
    def __init__(self, session_manager: Optional[HttpSessionManager] = None):
        """
        Initialize resource context.
        
        Args:
            session_manager: HTTP session manager instance
        """
        self.session_manager = session_manager or HttpSessionManager()
        self._temp_files = []
        self._temp_dirs = []
    
    @asynccontextmanager
    async def managed_operation(self, temp_dir_prefix: str = "ddb_operation_"):
        """
        Context manager for operations requiring both files and HTTP sessions.
        
        Args:
            temp_dir_prefix: Prefix for temporary directory
            
        Yields:
            tuple: (temp_dir_path, session_manager)
        """
        with FileManager.temporary_directory(prefix=temp_dir_prefix) as temp_dir:
            try:
                yield temp_dir, self.session_manager
            except Exception as e:
                logger.error(f"Error in managed operation: {e}")
                raise
            finally:
                # Session cleanup handled by session manager context
                pass
    
    async def cleanup(self):
        """Cleanup all managed resources."""
        await self.session_manager.close_all_sessions()


# Global instances for application use
file_manager = FileManager()
session_manager = HttpSessionManager()
resource_context = ResourceContext(session_manager)

# Utility functions for backwards compatibility
@contextmanager
def temp_file(suffix: str = "", prefix: str = "ddb_"):
    """Create a temporary file with automatic cleanup."""
    with file_manager.temporary_file(suffix=suffix, prefix=prefix) as temp_path:
        yield temp_path

@contextmanager  
def temp_directory(prefix: str = "ddb_"):
    """Create a temporary directory with automatic cleanup."""
    with file_manager.temporary_directory(prefix=prefix) as temp_path:
        yield temp_path

@asynccontextmanager
async def http_session():
    """Get a managed HTTP session."""
    async with session_manager.get_session() as session:
        yield session

# Application shutdown handler
async def cleanup_resources():
    """Cleanup all global resources. Call this on application shutdown."""
    logger.info("Cleaning up global resources...")
    await resource_context.cleanup()
    logger.info("Resource cleanup completed")