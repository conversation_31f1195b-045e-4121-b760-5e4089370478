"""
Centralized authorization system for the DDB SharePoint RAG application.
Provides decorators and utilities for admin access control.
"""

import logging
from typing import Optional, Dict, Any, Callable
from functools import wraps
from fastapi import Request, HTTPException, Depends, status
from config import settings

logger = logging.getLogger(__name__)

class AuthorizationError(Exception):
    """Custom exception for authorization failures."""
    pass

class AdminChecker:
    """Centralized admin authorization logic."""
    
    def __init__(self):
        """Initialize admin checker with configuration."""
        self._admin_emails_cache = None
        self._cache_timestamp = None
        
    def _get_admin_emails_set(self) -> set:
        """Get admin emails as a set, with basic caching."""
        import time
        current_time = time.time()
        
        # Refresh cache every 5 minutes
        if (self._admin_emails_cache is None or 
            self._cache_timestamp is None or 
            current_time - self._cache_timestamp > 300):
            
            self._admin_emails_cache = {
                email.strip().lower()
                for email in settings.ADMIN_EMAILS.split(",")
                if email.strip()
            }
            self._cache_timestamp = current_time
            logger.debug(f"Refreshed admin emails cache with {len(self._admin_emails_cache)} emails")
        
        return self._admin_emails_cache
    
    def is_admin_user(self, session: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """
        Check if the current session represents an admin user.
        
        Args:
            session: FastAPI session dictionary
            
        Returns:
            tuple: (is_admin: bool, user_identifier: Optional[str])
        """
        admin_emails_set = self._get_admin_emails_set()
        
        # Check 1: Basic Auth Admin User
        if (
            "basic_user" in session
            and session["basic_user"].get("authenticated")
            and session["basic_user"].get("username") == settings.USERNAME
        ):
            user_identifier = settings.USERNAME
            logger.debug(f"Admin access granted via Basic Auth user: {user_identifier}")
            return True, user_identifier
        
        # Check 2: Microsoft Logged-in User in Admin Emails List
        if (
            "ms_user" in session
            and session["ms_user"].get("email")
            and session["ms_user"]["email"].lower() in admin_emails_set
        ):
            user_identifier = session["ms_user"]["email"]
            logger.debug(f"Admin access granted via Microsoft email: {user_identifier}")
            return True, user_identifier
        
        # Not an admin - try to get identifier for logging
        basic_user_info = session.get("basic_user", {})
        ms_user_info = session.get("ms_user", {})
        user_identifier = (
            basic_user_info.get("username")
            or ms_user_info.get("email")
            or "Unknown User"
        )
        
        logger.debug(f"Admin access denied for user: {user_identifier}")
        return False, user_identifier
    
    def require_admin_access(self, session: Dict[str, Any], 
                           action_description: str = "access this resource") -> str:
        """
        Require admin access, raising HTTPException if not authorized.
        
        Args:
            session: FastAPI session dictionary
            action_description: Description of the action being attempted
            
        Returns:
            str: User identifier of the admin user
            
        Raises:
            HTTPException: If user is not authorized
        """
        is_admin, user_identifier = self.is_admin_user(session)
        
        if not is_admin:
            logger.warning(
                f"Unauthorized attempt to {action_description} by user: {user_identifier}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Administrative privileges required to {action_description}."
            )
        
        logger.info(f"Admin access granted to {user_identifier} for: {action_description}")
        return user_identifier


# Global admin checker instance
admin_checker = AdminChecker()

# FastAPI Dependencies
async def require_admin(request: Request) -> str:
    """
    FastAPI dependency that requires admin access.
    
    Args:
        request: FastAPI request object
        
    Returns:
        str: User identifier of the admin user
        
    Raises:
        HTTPException: If user is not authorized
    """
    session = request.session
    return admin_checker.require_admin_access(session, "perform administrative actions")

async def check_admin_status(request: Request) -> tuple[bool, str]:
    """
    FastAPI dependency that checks admin status without raising exceptions.
    
    Args:
        request: FastAPI request object
        
    Returns:
        tuple: (is_admin: bool, user_identifier: str)
    """
    session = request.session
    is_admin, user_identifier = admin_checker.is_admin_user(session)
    return is_admin, user_identifier or "Unknown User"

# Dependency aliases for common use cases
AdminRequired = Depends(require_admin)
AdminStatus = Depends(check_admin_status)

def admin_required(action_description: str = "access this resource"):
    """
    Decorator factory for protecting functions that require admin access.
    
    Args:
        action_description: Description of the protected action
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Look for request object in args or kwargs
            request = None
            
            # Check args for Request object
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            # Check kwargs for request
            if request is None:
                request = kwargs.get('request')
            
            if request is None:
                raise AuthorizationError("Request object not found for admin authorization")
            
            # Perform admin check
            session = request.session
            admin_checker.require_admin_access(session, action_description)
            
            # Call the original function
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator

def conditional_admin_required(condition_func: Callable[[Request], bool],
                             action_description: str = "access this resource"):
    """
    Decorator factory for conditionally requiring admin access.
    
    Args:
        condition_func: Function that takes a Request and returns bool indicating if admin is required
        action_description: Description of the protected action
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Look for request object
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if request is None:
                request = kwargs.get('request')
            
            if request is None:
                raise AuthorizationError("Request object not found for conditional admin authorization")
            
            # Check if admin is required for this request
            if condition_func(request):
                session = request.session
                admin_checker.require_admin_access(session, action_description)
            
            # Call the original function
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator

# Utility functions
def get_user_identifier(session: Dict[str, Any]) -> str:
    """
    Get a user identifier from session for logging purposes.
    
    Args:
        session: FastAPI session dictionary
        
    Returns:
        str: User identifier
    """
    basic_user_info = session.get("basic_user", {})
    ms_user_info = session.get("ms_user", {})
    
    return (
        basic_user_info.get("username")
        or ms_user_info.get("email")
        or "Unknown User"
    )

def log_admin_action(session: Dict[str, Any], action: str, 
                    success: bool = True, error: Optional[str] = None):
    """
    Log administrative actions for audit purposes.
    
    Args:
        session: FastAPI session dictionary
        action: Description of the action performed
        success: Whether the action was successful
        error: Error message if action failed
    """
    user_identifier = get_user_identifier(session)
    is_admin, _ = admin_checker.is_admin_user(session)
    
    if success:
        logger.info(f"Admin action performed by {user_identifier}: {action}")
    else:
        logger.error(f"Admin action failed for {user_identifier}: {action} - Error: {error}")
    
    # Also log if non-admin tried to perform admin action
    if not is_admin:
        logger.warning(f"Non-admin user {user_identifier} attempted admin action: {action}")

# Pre-configured decorators for common scenarios
admin_only = admin_required("perform administrative functions")
import_only = admin_required("import SharePoint content")
manage_documents = admin_required("manage documents")
sync_sharepoint = admin_required("synchronize SharePoint data")