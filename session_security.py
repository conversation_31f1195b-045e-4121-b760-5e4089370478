"""
Secure session management utilities for the DDB SharePoint RAG application.
Provides atomic session operations and token encryption to prevent session-related vulnerabilities.
"""

import logging
import time
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet
import base64
import os
from config import settings

logger = logging.getLogger(__name__)

class SecureSessionManager:
    """Handles secure session operations with atomic updates and token encryption."""
    
    def __init__(self):
        """Initialize the session manager with encryption key."""
        # Generate or retrieve encryption key for session data
        self._init_encryption_key()
    
    def _init_encryption_key(self):
        """Initialize encryption key for session token encryption."""
        # Use a derived key from the session secret for consistency
        session_secret = settings.SESSION_SECRET_KEY.get_secret_value()
        # Create a deterministic key from the session secret
        key_material = session_secret.encode('utf-8')[:32].ljust(32, b'0')
        self.encryption_key = base64.urlsafe_b64encode(key_material)
        self.cipher = Fernet(self.encryption_key)
    
    def encrypt_token(self, token: str) -> str:
        """Encrypt a token for secure session storage."""
        try:
            encrypted = self.cipher.encrypt(token.encode('utf-8'))
            return base64.urlsafe_b64encode(encrypted).decode('utf-8')
        except Exception as e:
            logger.error(f"Failed to encrypt token: {e}")
            raise
    
    def decrypt_token(self, encrypted_token: str) -> str:
        """Decrypt a token from session storage."""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_token.encode('utf-8'))
            decrypted = self.cipher.decrypt(encrypted_bytes)
            return decrypted.decode('utf-8')
        except Exception as e:
            logger.error(f"Failed to decrypt token: {e}")
            raise
    
    def atomic_session_update(self, session: Dict[str, Any], updates: Dict[str, Any], 
                            preserve_keys: Optional[list] = None) -> bool:
        """
        Atomically update session data.
        
        Args:
            session: The session dictionary to update
            updates: New session data to set
            preserve_keys: List of keys to preserve from existing session
            
        Returns:
            bool: True if update was successful
        """
        try:
            # Preserve specified keys before clearing
            preserved_data = {}
            if preserve_keys:
                for key in preserve_keys:
                    if key in session:
                        preserved_data[key] = session[key]
            
            # Clear session atomically
            session.clear()
            
            # Restore preserved data
            session.update(preserved_data)
            
            # Apply new updates
            session.update(updates)
            
            logger.info(f"Session updated atomically with {len(updates)} new items")
            return True
            
        except Exception as e:
            logger.error(f"Failed to atomically update session: {e}")
            return False
    
    def create_microsoft_session_data(self, token_result: Dict[str, Any], 
                                    email: str, name: str) -> Dict[str, Any]:
        """
        Create secure session data for Microsoft authentication.
        
        Args:
            token_result: Token response from MSAL
            email: User email
            name: User display name
            
        Returns:
            Dict with session data including encrypted tokens
        """
        session_data = {
            "ms_user": {"email": email, "name": name},
            "authenticated": True,
            "auth_method": "microsoft",
            "auth_timestamp": time.time()
        }
        
        # Encrypt and store token if available
        access_token = token_result.get("access_token")
        expires_in = token_result.get("expires_in")
        
        if access_token and expires_in:
            try:
                encrypted_token = self.encrypt_token(access_token)
                expires_on = time.time() + expires_in
                
                session_data["ms_token_cache"] = {
                    "encrypted_access_token": encrypted_token,
                    "expires_on": expires_on,
                }
                logger.info("Token encrypted and stored in session")
            except Exception as e:
                logger.error(f"Failed to encrypt token for session: {e}")
                # Continue without token storage if encryption fails
        else:
            logger.warning("No valid token received for session storage")
        
        return session_data
    
    def get_decrypted_token(self, session: Dict[str, Any]) -> Optional[str]:
        """
        Retrieve and decrypt access token from session.
        
        Args:
            session: Session dictionary
            
        Returns:
            Decrypted access token or None if not available/expired
        """
        token_cache = session.get("ms_token_cache", {})
        encrypted_token = token_cache.get("encrypted_access_token")
        expires_on = token_cache.get("expires_on")
        
        if not encrypted_token or not expires_on:
            return None
        
        # Check if token is expired
        if time.time() >= expires_on:
            logger.info("Access token has expired")
            return None
        
        try:
            return self.decrypt_token(encrypted_token)
        except Exception as e:
            logger.error(f"Failed to decrypt session token: {e}")
            return None
    
    def clear_session_safely(self, session: Dict[str, Any], 
                           preserve_keys: Optional[list] = None) -> bool:
        """
        Safely clear session data with optional key preservation.
        
        Args:
            session: Session dictionary to clear
            preserve_keys: Keys to preserve during clearing
            
        Returns:
            bool: True if successful
        """
        try:
            if preserve_keys:
                preserved = {key: session.get(key) for key in preserve_keys if key in session}
                session.clear()
                session.update(preserved)
            else:
                session.clear()
            
            logger.info("Session cleared safely")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear session safely: {e}")
            return False
    
    def validate_session(self, session: Dict[str, Any]) -> bool:
        """
        Validate session integrity and token freshness.
        
        Args:
            session: Session dictionary to validate
            
        Returns:
            bool: True if session is valid
        """
        try:
            # Check if authenticated
            if not session.get("authenticated"):
                return False
            
            # Check authentication method
            auth_method = session.get("auth_method")
            if auth_method not in ["microsoft", "basic"]:
                return False
            
            # For Microsoft auth, validate token freshness
            if auth_method == "microsoft":
                if not session.get("ms_user"):
                    return False
                
                # Check if token exists and is not expired
                token = self.get_decrypted_token(session)
                if not token:
                    logger.info("Session invalid: no valid token")
                    return False
            
            # For basic auth, check required fields
            elif auth_method == "basic":
                basic_user = session.get("basic_user", {})
                if not basic_user.get("authenticated") or not basic_user.get("username"):
                    return False
            
            logger.debug("Session validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Session validation failed: {e}")
            return False

# Global session manager instance
session_manager = SecureSessionManager()