# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **DDB SharePoint RAG Application** - a Retrieval-Augmented Generation system that integrates with Microsoft SharePoint to provide AI-powered document search and querying capabilities. The application is built using FastAPI, LlamaIndex, OpenAI, and Microsoft Graph API.

### Key Features
- SharePoint integration via Microsoft Graph API for document access
- RAG system using LlamaIndex with OpenAI embeddings and Cohere reranking
- Web-based chat interface for document querying
- Support for multiple file formats (PDF, DOCX, PPTX, images with OCR)
- Microsoft OAuth2 authentication
- Configurable for both local and cloud deployment

## Architecture

### Core Components

1. **app.py** - Main FastAPI application with routes for:
   - Authentication and session management
   - Document upload and management
   - RAG query processing
   - SharePoint integration endpoints

2. **config.py** - Centralized configuration using Pydantic settings:
   - Environment variable management
   - SharePoint and API key configuration
   - File handling and storage paths

3. **sharepoint_client.py** - Microsoft Graph API client for:
   - SharePoint site and library access
   - File browsing and downloading
   - Token-based authentication

4. **rag_app.py** - Standalone RAG implementation using LlamaIndex

### Key Dependencies
- **FastAPI** - Web framework and API
- **LlamaIndex** - RAG framework with OpenAI integration
- **MSAL** - Microsoft Authentication Library
- **Pydantic** - Configuration and data validation
- **Jinja2** - HTML templating

## Development Commands

### Environment Setup
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Alternative: conda environment
conda env create -f environment.yml
conda activate rag-env
```

### Running the Application
```bash
# Development server with auto-reload
uvicorn app:app --host localhost --port 8082 --reload

# Production server
uvicorn app:app --host 0.0.0.0 --port 8082 --workers 1
```

### Configuration
- Copy `.env.example` to `.env` and configure required environment variables
- Required variables: `OPENAI_API_KEY`, `COHERE_API_KEY`, `SESSION_SECRET_KEY`
- For SharePoint: `MS_CLIENT_ID`, `MS_CLIENT_SECRET`, `MS_TENANT_ID`

## Storage Architecture

The application uses local file storage with the following structure:
- `storage/` - Main storage directory
- `storage/data/` - Document storage and token caches
- `storage/uploads/` - Temporary upload files
- `storage/temp/` - SharePoint import temporary files
- Vector indices and document stores are persisted in storage directory

## SharePoint Integration

### Authentication Flow
- Uses Microsoft MSAL for OAuth2 authentication
- Implements token caching and refresh mechanisms
- Supports both interactive and application-only authentication

### File Operations
- Browse SharePoint sites, drives, and folders
- Download files from SharePoint libraries
- Import documents directly into RAG system
- Configurable target folders and libraries

## Deployment Options

### Local Development
- Uses SQLite-based storage
- Default port 8082
- Auto-reload enabled

### Docker Deployment
```bash
docker build -t ddb-sharepoint .
docker run -p 8082:8082 ddb-sharepoint
```

### Cloud Deployment (Render.com)
- Configured via `render.yaml`
- Persistent disk storage mounted at `/opt/render/project/src/storage`
- Environment variables managed through Render dashboard
- Tesseract OCR pre-installed for image processing

## Security Considerations

- All API keys stored as environment variables
- Session-based authentication with secure session keys
- File upload restrictions and size limits
- SharePoint permissions respected through Microsoft Graph API
- No hardcoded credentials in source code

## File Processing

### Supported Formats
- Documents: PDF, DOCX, PPTX, TXT, MD
- Images: PNG, JPG, JPEG, GIF, BMP (with OCR via Tesseract)
- Default file size limit: 50MB

### Processing Pipeline
1. File upload/import from SharePoint
2. Content extraction (text, OCR for images)
3. Document chunking and embedding generation
4. Vector store indexing
5. Query processing with reranking

## Important Notes

- No formal test suite is currently implemented
- Uses LlamaIndex's built-in document processing capabilities
- SharePoint integration requires proper Azure AD app registration
- Application supports both basic auth and Microsoft OAuth2
- Vector indices are automatically persisted and loaded on startup