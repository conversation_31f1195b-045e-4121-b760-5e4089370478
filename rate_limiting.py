"""
Rate limiting and retry logic for Microsoft Graph API calls.
Implements exponential backoff, request throttling, and 429 handling.
"""

import asyncio
import logging
import time
import random
from typing import Optional, Dict, Any, Callable, Union
from dataclasses import dataclass
from contextlib import asynccontextmanager
import aiohttp
from config import settings

logger = logging.getLogger(__name__)

@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_retries: int = 3
    base_delay: float = 1.0  # Base delay in seconds
    max_delay: float = 60.0  # Maximum delay in seconds
    exponential_base: float = 2.0
    jitter: bool = True  # Add randomization to prevent thundering herd

@dataclass
class RateLimitConfig:
    """Configuration for rate limiting."""
    requests_per_second: float = 10.0  # Conservative limit for Graph API
    burst_size: int = 20  # Allow bursts up to this size
    window_size: float = 60.0  # Time window for rate limiting in seconds

class RateLimitExceeded(Exception):
    """Raised when rate limit is exceeded and cannot be retried."""
    pass

class GraphAPIRateLimiter:
    """
    Rate limiter specifically designed for Microsoft Graph API.
    Implements token bucket algorithm with adaptive rate limiting.
    """
    
    def __init__(self, config: RateLimitConfig):
        """Initialize rate limiter with configuration."""
        self.config = config
        self.tokens = float(config.burst_size)  # Start with full bucket
        self.last_update = time.time()
        self.lock = asyncio.Lock()
        self.request_times = []  # Track request timing for adaptive limiting
        
        # Microsoft Graph specific limits
        self.graph_limits = {
            'default': 10.0,  # requests per second
            'search': 3.0,    # search endpoints are more restricted
            'batch': 5.0,     # batch requests
        }
        
    async def acquire(self, endpoint_type: str = 'default') -> bool:
        """
        Acquire permission to make a request.
        
        Args:
            endpoint_type: Type of endpoint ('default', 'search', 'batch')
            
        Returns:
            bool: True if request can proceed
        """
        async with self.lock:
            now = time.time()
            
            # Replenish tokens based on time elapsed
            time_passed = now - self.last_update
            rate = self.graph_limits.get(endpoint_type, self.config.requests_per_second)
            tokens_to_add = time_passed * rate
            
            self.tokens = min(self.config.burst_size, self.tokens + tokens_to_add)
            self.last_update = now
            
            # Check if we have tokens available
            if self.tokens >= 1.0:
                self.tokens -= 1.0
                
                # Track request for adaptive rate limiting
                self.request_times.append(now)
                # Keep only last minute of requests
                self.request_times = [t for t in self.request_times if now - t < 60.0]
                
                return True
            
            return False
    
    async def wait_for_token(self, endpoint_type: str = 'default', timeout: float = 30.0):
        """
        Wait for a token to become available.
        
        Args:
            endpoint_type: Type of endpoint
            timeout: Maximum time to wait
            
        Raises:
            RateLimitExceeded: If timeout is reached
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if await self.acquire(endpoint_type):
                return
            
            # Calculate wait time until next token is available
            rate = self.graph_limits.get(endpoint_type, self.config.requests_per_second)
            wait_time = min(1.0 / rate, 0.1)  # Wait at most 100ms
            
            await asyncio.sleep(wait_time)
        
        raise RateLimitExceeded(f"Rate limit timeout exceeded for endpoint type: {endpoint_type}")
    
    def get_current_rate(self) -> float:
        """Get current request rate over the last minute."""
        now = time.time()
        recent_requests = [t for t in self.request_times if now - t < 60.0]
        return len(recent_requests) / 60.0

class RetryHandler:
    """
    Handles retry logic with exponential backoff for HTTP requests.
    Specifically designed for Microsoft Graph API error codes.
    """
    
    def __init__(self, config: RetryConfig):
        """Initialize retry handler with configuration."""
        self.config = config
        
        # HTTP status codes that should be retried
        self.retryable_status_codes = {
            429,  # Too Many Requests
            500,  # Internal Server Error
            502,  # Bad Gateway
            503,  # Service Unavailable
            504,  # Gateway Timeout
        }
        
        # Graph API specific error codes that should be retried
        self.retryable_graph_errors = {
            'TooManyRequests',
            'ServiceUnavailable',
            'InternalServerError',
            'Timeout',
        }
    
    def should_retry(self, response: aiohttp.ClientResponse, error: Optional[Exception] = None) -> bool:
        """
        Determine if a request should be retried.
        
        Args:
            response: HTTP response object
            error: Exception that occurred (if any)
            
        Returns:
            bool: True if request should be retried
        """
        # Always retry on connection errors
        if error and isinstance(error, (aiohttp.ClientConnectionError, asyncio.TimeoutError)):
            return True
        
        # Check HTTP status codes
        if response and response.status in self.retryable_status_codes:
            return True
        
        return False
    
    def get_retry_delay(self, attempt: int, response: Optional[aiohttp.ClientResponse] = None) -> float:
        """
        Calculate delay before next retry attempt.
        
        Args:
            attempt: Current attempt number (0-based)
            response: HTTP response (to check for Retry-After header)
            
        Returns:
            float: Delay in seconds
        """
        # Check for Retry-After header (Microsoft Graph API provides this)
        if response and 'Retry-After' in response.headers:
            try:
                retry_after = float(response.headers['Retry-After'])
                # Add some jitter and cap at max_delay
                jitter_factor = 1 + (0.1 * random.random()) if self.config.jitter else 1
                return min(retry_after * jitter_factor, self.config.max_delay)
            except (ValueError, TypeError):
                pass
        
        # Exponential backoff calculation
        delay = self.config.base_delay * (self.config.exponential_base ** attempt)
        
        # Add jitter to prevent thundering herd
        if self.config.jitter:
            jitter_factor = 0.5 + (0.5 * random.random())  # 50-100% of calculated delay
            delay *= jitter_factor
        
        return min(delay, self.config.max_delay)
    
    async def log_retry_attempt(self, attempt: int, delay: float, 
                              response: Optional[aiohttp.ClientResponse] = None,
                              error: Optional[Exception] = None):
        """Log retry attempt details."""
        if response:
            logger.warning(
                f"HTTP {response.status} error, retrying in {delay:.2f}s "
                f"(attempt {attempt + 1}/{self.config.max_retries})"
            )
        elif error:
            logger.warning(
                f"Request error ({type(error).__name__}), retrying in {delay:.2f}s "
                f"(attempt {attempt + 1}/{self.config.max_retries})"
            )

class GraphAPIClient:
    """
    HTTP client wrapper with rate limiting and retry logic for Microsoft Graph API.
    """
    
    def __init__(self, 
                 rate_limit_config: Optional[RateLimitConfig] = None,
                 retry_config: Optional[RetryConfig] = None):
        """Initialize Graph API client with rate limiting and retry."""
        self.rate_limiter = GraphAPIRateLimiter(
            rate_limit_config or RateLimitConfig()
        )
        self.retry_handler = RetryHandler(
            retry_config or RetryConfig()
        )
        self.session = None
        
    @asynccontextmanager
    async def get_session(self):
        """Get or create HTTP session."""
        if self.session is None or self.session.closed:
            connector = aiohttp.TCPConnector(
                limit=50,  # Total connection pool size
                limit_per_host=10,  # Max connections per host
                ttl_dns_cache=300,
                use_dns_cache=True,
            )
            
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
            )
        
        try:
            yield self.session
        finally:
            pass  # Keep session open for reuse
    
    async def close(self):
        """Close the HTTP session."""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def request(self, method: str, url: str, 
                     endpoint_type: str = 'default',
                     **kwargs) -> aiohttp.ClientResponse:
        """
        Make an HTTP request with rate limiting and retry logic.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            url: Request URL
            endpoint_type: Type of endpoint for rate limiting
            **kwargs: Additional arguments for aiohttp request
            
        Returns:
            aiohttp.ClientResponse: HTTP response
            
        Raises:
            RateLimitExceeded: If rate limiting fails
            aiohttp.ClientError: If all retries are exhausted
        """
        # Wait for rate limit permission
        await self.rate_limiter.wait_for_token(endpoint_type)
        
        for attempt in range(self.retry_handler.config.max_retries + 1):
            try:
                async with self.get_session() as session:
                    response = await session.request(method, url, **kwargs)
                    
                    # Check if we should retry
                    if attempt < self.retry_handler.config.max_retries and \
                       self.retry_handler.should_retry(response):
                        
                        delay = self.retry_handler.get_retry_delay(attempt, response)
                        await self.retry_handler.log_retry_attempt(attempt, delay, response)
                        
                        # Don't consume rate limit token for retries
                        await asyncio.sleep(delay)
                        continue
                    
                    # Log successful request after retries
                    if attempt > 0:
                        logger.info(f"Request succeeded after {attempt} retries")
                    
                    return response
                    
            except Exception as error:
                # Check if we should retry on error
                if attempt < self.retry_handler.config.max_retries and \
                   self.retry_handler.should_retry(None, error):
                    
                    delay = self.retry_handler.get_retry_delay(attempt)
                    await self.retry_handler.log_retry_attempt(attempt, delay, error=error)
                    
                    await asyncio.sleep(delay)
                    continue
                
                # Re-raise if we can't retry
                raise
        
        # This should not be reached, but just in case
        raise Exception("All retry attempts failed")
    
    async def get(self, url: str, endpoint_type: str = 'default', **kwargs) -> aiohttp.ClientResponse:
        """Make a GET request."""
        return await self.request('GET', url, endpoint_type, **kwargs)
    
    async def post(self, url: str, endpoint_type: str = 'default', **kwargs) -> aiohttp.ClientResponse:
        """Make a POST request."""
        return await self.request('POST', url, endpoint_type, **kwargs)
    
    async def patch(self, url: str, endpoint_type: str = 'default', **kwargs) -> aiohttp.ClientResponse:
        """Make a PATCH request."""
        return await self.request('PATCH', url, endpoint_type, **kwargs)
    
    async def delete(self, url: str, endpoint_type: str = 'default', **kwargs) -> aiohttp.ClientResponse:
        """Make a DELETE request."""
        return await self.request('DELETE', url, endpoint_type, **kwargs)

# Global instance for SharePoint client usage
graph_api_client = GraphAPIClient(
    rate_limit_config=RateLimitConfig(
        requests_per_second=8.0,  # Conservative rate for SharePoint
        burst_size=15,
        window_size=60.0
    ),
    retry_config=RetryConfig(
        max_retries=3,
        base_delay=1.0,
        max_delay=30.0,
        exponential_base=2.0,
        jitter=True
    )
)

# Helper function for backwards compatibility
async def make_graph_request(method: str, url: str, **kwargs) -> aiohttp.ClientResponse:
    """
    Make a rate-limited Graph API request.
    
    Args:
        method: HTTP method
        url: Request URL
        **kwargs: Additional request arguments
        
    Returns:
        aiohttp.ClientResponse: HTTP response
    """
    # Determine endpoint type from URL
    endpoint_type = 'default'
    if '/search' in url.lower():
        endpoint_type = 'search'
    elif '$batch' in url.lower():
        endpoint_type = 'batch'
    
    return await graph_api_client.request(method, url, endpoint_type, **kwargs)

# Cleanup function
async def cleanup_rate_limiter():
    """Cleanup rate limiter resources."""
    await graph_api_client.close()
    logger.info("Rate limiter resources cleaned up")