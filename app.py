import os, sys, json, logging, uuid, time, socket
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List
from urllib.parse import unquote, quote

from fastapi import (
    FastAPI,
    Request,
    Depends,
    HTTPException,
    Query,
    BackgroundTasks,
    UploadFile,
    File,
    Form,
    status,
    Header,
    Response,
)
from fastapi.responses import (
    JSONResponse,
    RedirectResponse,
    PlainTextResponse,
    HTMLResponse,
    StreamingResponse,
    FileResponse,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from starlette.middleware.sessions import SessionMiddleware
from starlette.routing import NoMatchFound
import mimetypes
import tempfile
import traceback
import asyncio
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv
from sharepoint_client import SharePointClient
from config import settings
from session_security import session_manager
from input_validation import validator, ValidationError
from csrf_protection import CSRFProtect, get_csrf_token, add_csrf_header_middleware
from authorization import AdminRequired, AdminStatus, admin_checker, log_admin_action
from index_locking import safe_index_ops, index_lock_manager, log_index_status
import msal

# Optional msal-extensions dependency: guard import
try:
    from msal_extensions import FilePersistence, PersistedTokenCache

    MSAL_EXTENSIONS_AVAILABLE = True
except ImportError:
    MSAL_EXTENSIONS_AVAILABLE = False
    FilePersistence = None  # Define dummy class/None if needed later
    PersistedTokenCache = None  # Define dummy class/None if needed later
    logging.warning("msal-extensions not available; persistent token cache disabled.")
# --- End guard ---
import aiohttp
from PIL import Image

# Optional OCR dependency: guard import
try:
    import pytesseract

    PYTESSERACT_AVAILABLE = True
except ImportError:
    PYTESSERACT_AVAILABLE = False
    logging.warning("pytesseract not available, image OCR will be disabled.")
import io
import shutil
import openai

# Try to import APScheduler, but don't fail if it's not installed
try:
    from apscheduler.schedulers.asyncio import AsyncIOScheduler

    APSCHEDULER_AVAILABLE = True
except ImportError:
    APSCHEDULER_AVAILABLE = False
    logging.warning("APScheduler not available; webhook renewal scheduling disabled.")

# Try to import PyPDF2, but don't fail if it's not available
try:
    import PyPDF2

    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    logging.warning(
        "PyPDF2 not available. PDF processing will use fallback methods only."
    )

from llama_index.core import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    StorageContext,
    load_index_from_storage,
    Settings as LlamaSettings,
    get_response_synthesizer,
)
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI as LlamaOpenAI
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.schema import MetadataMode, TextNode, Document

# Optional Cohere reranker: guard import
try:
    from llama_index.postprocessor.cohere_rerank import CohereRerank

    COHERE_RERANK_AVAILABLE = True
except ImportError:
    COHERE_RERANK_AVAILABLE = False
    logging.warning("CohereRerank not available; reranking disabled.")

# Load environment variables first
load_dotenv()

# ── basic logging ─────────────────────────────────────────────────────────────
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s  %(levelname)-8s  %(name)s │ %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger("ddb‑rag")


# ── helpers to figure out *one* canonical base URL ────────────────────────────
def _detect_base_url() -> str:
    """
    Guarantee that every redirect and every piece of client‑side JS
    talks to *exactly* the same origin, so the browser sends the session
    cookie back on XHR requests (localhost ≠ 127.0.0.1 otherwise).
    """
    # Render / Railway / Fly.io etc.
    if ext := os.getenv("RENDER_EXTERNAL_URL"):
        return ext.rstrip("/")

    # Determine port
    port_env = int(os.getenv("PORT", 8082))
    # In development, always use localhost
    if os.getenv("ENV", "development").lower() == "development":
        return f"http://localhost:{port_env}"

    # For other environments, derive from HOST env var
    host_env = os.getenv("HOST") or "127.0.0.1"
    # If HOST is 0.0.0.0 or 127.0.0.1, default to localhost
    if host_env in ("0.0.0.0", "127.0.0.1"):
        host_env = "localhost"
    else:
        # Validate IP format; fallback to localhost on error
        try:
            socket.inet_aton(host_env)
        except OSError:
            host_env = "localhost"

    return f"http://{host_env}:{port_env}"


BASE_URL = _detect_base_url()  # ← http://127.0.0.1:8082
REDIRECT_PATH = "/auth/callback"
EXPECTED_REDIRECT = f"{BASE_URL}{REDIRECT_PATH}"
logger.info(f"OAuth redirect URI →  {EXPECTED_REDIRECT}")

# Application host and port for direct execution
APP_HOST = os.getenv("HOST", "127.0.0.1")
APP_PORT = int(os.getenv("PORT", 8082))


# Helper function to format responses before sending JSON
def format_response(text: str) -> str:
    return text


# ── MSAL configuration ────────────────────────────────────────────────────────
MSAL_SCOPES = [
    "User.Read",
    "Sites.Read.All",
    "Files.Read.All",
    "Sites.ReadWrite.All",
]
MSAL_APP_SCOPE = ["https://graph.microsoft.com/.default"]

# Build the MSAL app instance used for user authentication
msal_app = None
if settings.MS_CLIENT_ID and settings.MS_CLIENT_SECRET and settings.MS_TENANT_ID:
    # Use msal-extensions for file-persistent token cache with automatic locking
    token_cache = None  # Default to in-memory cache
    if MSAL_EXTENSIONS_AVAILABLE:
        try:
            cache_path = settings.DATA_DIR / "token_caches" / "msal_cache.bin"
            cache_path.parent.mkdir(parents=True, exist_ok=True)
            persistence = FilePersistence(str(cache_path))
            token_cache = PersistedTokenCache(persistence)
            logger.info("Using file-persistent MSAL token cache.")
        except Exception as e:
            logger.error(
                f"Failed to initialize file-persistent token cache: {e}. Falling back to in-memory cache."
            )
            token_cache = None  # Ensure fallback on error
    else:
        logger.warning("msal-extensions not found. Using in-memory MSAL token cache.")

    msal_app = msal.ConfidentialClientApplication(
        settings.MS_CLIENT_ID,
        authority=f"https://login.microsoftonline.com/{settings.MS_TENANT_ID}",
        client_credential=settings.MS_CLIENT_SECRET.get_secret_value(),
        token_cache=token_cache,  # Pass the persistent cache or None
    )
else:
    logger.warning(
        "Microsoft credentials (MS_CLIENT_ID, MS_CLIENT_SECRET, MS_TENANT_ID) not configured. SharePoint delegated auth will not work."
    )

# ── FastAPI app and middle‑wares ──────────────────────────────────────────────
app = FastAPI()

# single session cookie, always bound to the host part of BASE_URL
_cookie_host = BASE_URL.split("://", 1)[1].split(":")[
    0
]  # e.g., 'localhost' or real domain
# Only set domain for real hosts; omit for localhost to create host-only cookie
cookie_domain = None if _cookie_host in ("localhost", "127.0.0.1") else _cookie_host
app.add_middleware(
    SessionMiddleware,
    secret_key=settings.SESSION_SECRET_KEY.get_secret_value(),
    session_cookie="rag_session",
    domain=cookie_domain,
    same_site="lax",
    max_age=3600 * 24 * 7,
    https_only=BASE_URL.startswith("https"),
)

# Initialize global variables
llm = None
embed_model = None
index = None  # Will be initialized in startup
startup_time = time.time()  # Track application startup time

# Near the top with other settings
OPENAI_MODEL = "gpt-3.5-turbo"  # Changed from gpt-4-turbo-preview to gpt-3.5-turbo

# Configure CORS with settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origin_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Type", "text/event-stream", "X-CSRF-Token"],
)

# Add CSRF protection middleware
app.middleware("http")(add_csrf_header_middleware)

# Global exception handlers
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler to catch any unhandled errors."""
    error_type = type(exc).__name__
    error_id = str(uuid.uuid4())[:8]  # Short error ID for tracking

    logger.error(
        f"Unhandled exception [{error_id}] ({error_type}) on {request.method} {request.url}: {exc}",
        exc_info=True
    )

    # Return appropriate error response based on request type
    if request.url.path.startswith("/api/") or "application/json" in request.headers.get("accept", ""):
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "error_id": error_id,
                "error_type": error_type,
                "details": "An unexpected error occurred. Please contact support with the error ID."
            }
        )
    else:
        # Return HTML error page for browser requests
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Internal Server Error",
                "error_message": "An unexpected error occurred",
                "error_details": f"Error ID: {error_id}. Please contact support.",
                "error_id": error_id
            },
            status_code=500
        )

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions with better error messages."""
    error_id = str(uuid.uuid4())[:8]

    logger.warning(
        f"HTTP exception [{error_id}] {exc.status_code} on {request.method} {request.url}: {exc.detail}"
    )

    if request.url.path.startswith("/api/") or "application/json" in request.headers.get("accept", ""):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.detail,
                "error_id": error_id,
                "status_code": exc.status_code
            }
        )
    else:
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": f"Error {exc.status_code}",
                "error_message": exc.detail,
                "error_details": f"Error ID: {error_id}",
                "error_id": error_id
            },
            status_code=exc.status_code
        )

# Mount static files with defensive check and templates
static_dir = Path("static")
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    logger.info("Static files mounted from 'static' directory")
else:
    logger.warning("'static' folder not found – skipping mount")
templates = Jinja2Templates(directory="templates")


@app.get("/config.js")
async def config_js():
    # the browser will evaluate this script before running its own JS
    return PlainTextResponse(
        f"window.__RAG_BASE = '{BASE_URL}';",
        media_type="application/javascript",
    )


@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint for monitoring application status."""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0",
            "components": component_initializer.get_status(),
            "uptime_seconds": time.time() - startup_time if 'startup_time' in globals() else 0,
            "checks": {}
        }

        warnings = []
        errors = []

        # Check critical components
        critical_components = ['llm', 'embed_model', 'index']
        all_critical_ready = all(health_status["components"].get(comp, False) for comp in critical_components)

        if not all_critical_ready:
            health_status["status"] = "initializing"
            health_status["checks"]["critical_components"] = {
                "status": "initializing",
                "details": f"Missing: {[comp for comp in critical_components if not health_status['components'].get(comp, False)]}"
            }
        else:
            health_status["checks"]["critical_components"] = {"status": "healthy"}

        # Check external services
        # OpenAI API connectivity
        try:
            if hasattr(app.state, 'llm') and app.state.llm:
                # Quick test of OpenAI connectivity
                test_response = await asyncio.wait_for(
                    asyncio.to_thread(lambda: "OpenAI connection test"),
                    timeout=5.0
                )
                health_status["checks"]["openai_api"] = {"status": "healthy"}
            else:
                health_status["checks"]["openai_api"] = {"status": "not_initialized"}
        except asyncio.TimeoutError:
            health_status["checks"]["openai_api"] = {"status": "timeout"}
            warnings.append("OpenAI API timeout")
        except Exception as e:
            health_status["checks"]["openai_api"] = {"status": "error", "error": str(e)}
            warnings.append(f"OpenAI API error: {str(e)}")

        # Cohere API connectivity (if enabled)
        if COHERE_RERANK_AVAILABLE and hasattr(app.state, 'reranker') and app.state.reranker:
            try:
                health_status["checks"]["cohere_api"] = {"status": "healthy"}
            except Exception as e:
                health_status["checks"]["cohere_api"] = {"status": "error", "error": str(e)}
                warnings.append(f"Cohere API error: {str(e)}")
        else:
            health_status["checks"]["cohere_api"] = {"status": "not_configured"}

        # SharePoint connectivity (if enabled)
        if settings.USE_SHAREPOINT and hasattr(app.state, 'sharepoint_client') and app.state.sharepoint_client:
            try:
                # Test SharePoint connectivity with a quick token check
                health_status["checks"]["sharepoint"] = {"status": "configured"}
            except Exception as e:
                health_status["checks"]["sharepoint"] = {"status": "error", "error": str(e)}
                warnings.append(f"SharePoint error: {str(e)}")
        else:
            health_status["checks"]["sharepoint"] = {"status": "not_configured"}

        # File system checks
        try:
            # Check if required directories exist and are writable
            required_dirs = [settings.STORAGE_DIR, settings.DATA_DIR, settings.UPLOAD_DIR, settings.TEMP_DIR]
            dir_status = {}
            for dir_path in required_dirs:
                if dir_path.exists():
                    if os.access(dir_path, os.W_OK):
                        dir_status[str(dir_path)] = "writable"
                    else:
                        dir_status[str(dir_path)] = "read_only"
                        warnings.append(f"Directory not writable: {dir_path}")
                else:
                    dir_status[str(dir_path)] = "missing"
                    errors.append(f"Required directory missing: {dir_path}")

            health_status["checks"]["filesystem"] = {
                "status": "healthy" if not errors else "error",
                "directories": dir_status
            }
        except Exception as e:
            health_status["checks"]["filesystem"] = {"status": "error", "error": str(e)}
            errors.append(f"Filesystem check error: {str(e)}")

        # Memory usage check
        try:
            import psutil
            memory = psutil.virtual_memory()
            health_status["checks"]["memory"] = {
                "status": "healthy" if memory.percent < 90 else "warning",
                "usage_percent": memory.percent,
                "available_gb": round(memory.available / (1024**3), 2)
            }
            if memory.percent > 90:
                warnings.append(f"High memory usage: {memory.percent}%")
        except ImportError:
            health_status["checks"]["memory"] = {"status": "unavailable", "reason": "psutil not installed"}
        except Exception as e:
            health_status["checks"]["memory"] = {"status": "error", "error": str(e)}

        # Determine overall status
        if errors:
            health_status["status"] = "unhealthy"
            health_status["errors"] = errors
            return JSONResponse(content=health_status, status_code=500)
        elif warnings:
            health_status["status"] = "degraded"
            health_status["warnings"] = warnings
            return JSONResponse(content=health_status, status_code=200)
        elif not all_critical_ready:
            health_status["status"] = "initializing"
            return JSONResponse(content=health_status, status_code=503)
        else:
            return JSONResponse(content=health_status, status_code=200)

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            content={
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            },
            status_code=500
        )


@app.get("/health/ready")
async def readiness_check():
    """Readiness check for Kubernetes/container orchestration."""
    try:
        # Check if critical components are ready
        if not hasattr(app.state, 'index') or app.state.index is None:
            return JSONResponse(
                content={"ready": False, "reason": "Vector index not loaded"},
                status_code=503
            )

        if not hasattr(app.state, 'query_engine') or app.state.query_engine is None:
            return JSONResponse(
                content={"ready": False, "reason": "Query engine not initialized"},
                status_code=503
            )

        return JSONResponse(content={"ready": True}, status_code=200)

    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            content={"ready": False, "reason": str(e)},
            status_code=500
        )


@app.get("/health/live")
async def liveness_check():
    """Liveness check for Kubernetes/container orchestration."""
    return JSONResponse(content={"alive": True}, status_code=200)


@app.get("/metrics")
async def metrics_endpoint():
    """Metrics endpoint for monitoring and observability."""
    try:
        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "uptime_seconds": time.time() - startup_time if 'startup_time' in globals() else 0,
            "application": {
                "name": "ddb-sharepoint-rag",
                "version": "1.0.0",
                "environment": os.getenv('ENV', 'development')
            },
            "components": component_initializer.get_status(),
            "performance": {}
        }

        # Add system metrics if psutil is available
        try:
            import psutil

            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            metrics["performance"]["cpu"] = {
                "usage_percent": cpu_percent,
                "count": psutil.cpu_count()
            }

            # Memory metrics
            memory = psutil.virtual_memory()
            metrics["performance"]["memory"] = {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "usage_percent": memory.percent
            }

            # Disk metrics for storage directories
            disk_metrics = {}
            for dir_name, dir_path in [
                ("storage", settings.STORAGE_DIR),
                ("data", settings.DATA_DIR),
                ("uploads", settings.UPLOAD_DIR),
                ("temp", settings.TEMP_DIR)
            ]:
                try:
                    if dir_path.exists():
                        disk_usage = psutil.disk_usage(str(dir_path))
                        disk_metrics[dir_name] = {
                            "total_gb": round(disk_usage.total / (1024**3), 2),
                            "free_gb": round(disk_usage.free / (1024**3), 2),
                            "used_gb": round(disk_usage.used / (1024**3), 2),
                            "usage_percent": round((disk_usage.used / disk_usage.total) * 100, 2)
                        }
                except Exception as e:
                    disk_metrics[dir_name] = {"error": str(e)}

            metrics["performance"]["disk"] = disk_metrics

        except ImportError:
            metrics["performance"]["note"] = "Install psutil for detailed system metrics"
        except Exception as e:
            metrics["performance"]["error"] = str(e)

        # Add index statistics if available
        if hasattr(app.state, 'index') and app.state.index:
            try:
                # Get document count from index
                docstore = app.state.index.docstore
                if hasattr(docstore, 'docs'):
                    metrics["index"] = {
                        "document_count": len(docstore.docs),
                        "status": "loaded"
                    }
                else:
                    metrics["index"] = {"status": "loaded", "document_count": "unknown"}
            except Exception as e:
                metrics["index"] = {"status": "error", "error": str(e)}
        else:
            metrics["index"] = {"status": "not_loaded"}

        return JSONResponse(content=metrics, status_code=200)

    except Exception as e:
        logger.error(f"Metrics endpoint failed: {e}")
        return JSONResponse(
            content={
                "error": "Failed to generate metrics",
                "timestamp": datetime.utcnow().isoformat(),
                "details": str(e)
            },
            status_code=500
        )


# --- Auth Helpers ---
def _extract_user(session):
    """Return a user-dict if either auth method is present."""
    if "basic_user" in session and session["basic_user"].get("authenticated"):
        return session["basic_user"]
    if "ms_user" in session:
        return session["ms_user"]
    return None


def user_is_admin(session):
    """Check if the current session represents an admin user."""
    is_admin, _ = admin_checker.is_admin_user(session)
    return is_admin


async def get_current_user(request: Request):
    """Get current user from session using either authentication method."""
    session = request.session
    # <<< Log session content whenever user is checked >>>
    logger.info(f"[Auth Check] Path: {request.url.path}, Session: {dict(session)}")
    # <<< End log >>>
    user = _extract_user(session)
    if user:
        logger.info(f"[Auth Check] User found: {user}")
        return user
    logger.warning(f"[Auth Check] No user found in session for path: {request.url.path}")
    raise HTTPException(
        status_code=401,
        detail="Not authenticated",
    )


# Create unified security object and dependency
security = HTTPBasic()
CurrentUser = Depends(get_current_user)


# --- Function to get Application Token ---
async def get_application_token() -> Optional[str]:
    """Acquire an access token for the application itself using client credentials."""
    if not msal_app:
        logger.error("MSAL application not initialized. Cannot get application token.")
        return None

    try:
        # Attempt to get token from cache first (MSAL handles caching)
        result = await asyncio.wait_for(
            asyncio.to_thread(msal_app.acquire_token_silent, MSAL_APP_SCOPE, None),
            timeout=10,
        )

        if not result:
            logger.info("No suitable app token in cache, acquiring new one...")
            result = await asyncio.wait_for(
                asyncio.to_thread(msal_app.acquire_token_for_client, MSAL_APP_SCOPE),
                timeout=10,
            )

        if "access_token" in result:
            logger.info("Successfully acquired application access token.")
            return result["access_token"]
        else:
            logger.error(
                f"Failed to acquire application token: {result.get('error_description', 'No error description')}"
            )
            return None
    except Exception as e:
        logger.error(f"Exception acquiring application token: {e}", exc_info=True)
        return None


@app.on_event("startup")
async def startup_event():
    """Initialize application state and create required directories and schedule heavy tasks."""
    logger.info("Starting application with document persistence...")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Current working directory: {os.getcwd()}")
    logger.info(f"Environment: {os.getenv('ENV', 'development')}")

    # <<< Add route logging >>>
    logger.info("Registered routes:")
    for r in app.routes:
        if hasattr(r, "methods"):  # Check if it's a route with methods
            try:
                logger.info(
                    f"  - Route: Path={r.path}, Name={r.name}, Methods={r.methods}"
                )
            except AttributeError:  # Handle routes without a name
                logger.info(f"  - Route: Path={r.path}, Methods={r.methods}")
        elif hasattr(r, "path"):  # Handle Mounts (like static files)
            logger.info(f"  - Mount: Path={r.path}")
        else:
            logger.info(f"  - Other: {r}")  # Log anything else unexpected
    # <<< End route logging >>>

    # Create required directories
    try:
        settings.STORAGE_DIR.mkdir(exist_ok=True)
        settings.DATA_DIR.mkdir(exist_ok=True)
        settings.UPLOAD_DIR.mkdir(exist_ok=True)
        settings.TEMP_DIR.mkdir(exist_ok=True)
        logger.info("Storage directory ready")
    except Exception as e:
        logger.error(f"Error creating directories: {e}")

    # Schedule heavy initialization in background
    loop = asyncio.get_running_loop()
    loop.create_task(initialise_heavy_components())
    logger.info("Scheduled background initialization of heavy components")


# Optimized component initializer with lazy loading and caching
class ComponentInitializer:
    """Manages lazy initialization of heavy components with caching."""

    def __init__(self):
        self._llm = None
        self._embed_model = None
        self._reranker = None
        self._sharepoint_client = None
        self._index = None
        self._query_engine = None
        self._initialization_lock = asyncio.Lock()
        self._component_status = {
            'llm': False,
            'embed_model': False,
            'reranker': False,
            'sharepoint_client': False,
            'index': False,
            'query_engine': False
        }

    async def get_llm(self):
        """Lazy initialization of LLM."""
        if self._llm is None:
            async with self._initialization_lock:
                if self._llm is None:  # Double-check pattern
                    logger.info("Initializing OpenAI LLM...")
                    start_time = time.time()
                    openai.api_key = settings.OPENAI_API_KEY.get_secret_value()
                    self._llm = await asyncio.to_thread(
                        LlamaOpenAI, model=OPENAI_MODEL, temperature=0.7, max_tokens=2000
                    )
                    self._component_status['llm'] = True
                    logger.info(f"OpenAI LLM initialized in {time.time() - start_time:.2f}s")
        return self._llm

    async def get_embed_model(self):
        """Lazy initialization of embedding model."""
        if self._embed_model is None:
            async with self._initialization_lock:
                if self._embed_model is None:
                    logger.info("Initializing embedding model...")
                    start_time = time.time()
                    self._embed_model = await asyncio.to_thread(OpenAIEmbedding)
                    self._component_status['embed_model'] = True
                    logger.info(f"Embedding model initialized in {time.time() - start_time:.2f}s")
        return self._embed_model

    async def get_reranker(self):
        """Lazy initialization of reranker."""
        if self._reranker is None and COHERE_RERANK_AVAILABLE:
            async with self._initialization_lock:
                if self._reranker is None:
                    try:
                        logger.info("Initializing Cohere reranker...")
                        start_time = time.time()
                        self._reranker = await asyncio.to_thread(
                            CohereRerank, api_key=settings.COHERE_API_KEY.get_secret_value()
                        )
                        self._component_status['reranker'] = True
                        logger.info(f"Cohere reranker initialized in {time.time() - start_time:.2f}s")
                    except Exception as e:
                        logger.error(f"Error initializing Cohere reranker: {e}")
                        self._reranker = None
        return self._reranker

    async def get_sharepoint_client(self):
        """Lazy initialization of SharePoint client."""
        if self._sharepoint_client is None and settings.USE_SHAREPOINT:
            async with self._initialization_lock:
                if self._sharepoint_client is None:
                    try:
                        logger.info("Initializing SharePoint client...")
                        start_time = time.time()
                        from resource_management import session_manager
                        self._sharepoint_client = await asyncio.to_thread(SharePointClient, session_manager)
                        self._component_status['sharepoint_client'] = True
                        logger.info(f"SharePoint client initialized in {time.time() - start_time:.2f}s")
                    except Exception as e:
                        logger.error(f"Error initializing SharePoint client: {e}")
                        self._sharepoint_client = None
        return self._sharepoint_client

    async def get_index(self):
        """Lazy initialization of vector index."""
        if self._index is None:
            async with self._initialization_lock:
                if self._index is None:
                    try:
                        logger.info("Loading vector index...")
                        start_time = time.time()
                        self._index = await load_index()
                        self._component_status['index'] = True
                        logger.info(f"Index loaded in {time.time() - start_time:.2f}s")
                    except Exception as e:
                        logger.error(f"Error loading index: {e}")
                        logger.warning("Creating empty index as fallback...")
                        self._index = await create_empty_index()
        return self._index

    async def get_query_engine(self):
        """Lazy initialization of query engine."""
        if self._query_engine is None:
            async with self._initialization_lock:
                if self._query_engine is None:
                    try:
                        logger.info("Creating query engine...")
                        start_time = time.time()
                        index = await self.get_index()
                        reranker = await self.get_reranker()
                        self._query_engine = get_query_engine(index, reranker)
                        self._component_status['query_engine'] = True
                        logger.info(f"Query engine created in {time.time() - start_time:.2f}s")
                    except Exception as e:
                        logger.error(f"Error creating query engine: {e}")
                        self._query_engine = None
        return self._query_engine

    def get_status(self):
        """Get initialization status of all components."""
        return self._component_status.copy()

# Global component initializer
component_initializer = ComponentInitializer()

# Background initialization moved out of startup_event
async def initialise_heavy_components():
    """Runs optimized initialization after the app has started."""
    try:
        logger.info("Starting optimized background initialization...")
        start_time = time.time()

        # Initialize critical components in parallel where possible
        tasks = []

        # Start LLM and embedding model initialization in parallel
        tasks.append(asyncio.create_task(component_initializer.get_llm()))
        tasks.append(asyncio.create_task(component_initializer.get_embed_model()))

        # Start reranker initialization
        if COHERE_RERANK_AVAILABLE:
            tasks.append(asyncio.create_task(component_initializer.get_reranker()))

        # Start SharePoint client initialization if enabled
        if settings.USE_SHAREPOINT:
            tasks.append(asyncio.create_task(component_initializer.get_sharepoint_client()))

        # Wait for all parallel tasks to complete
        await asyncio.gather(*tasks, return_exceptions=True)

        # Initialize index (depends on embed model)
        await component_initializer.get_index()

        # Initialize query engine (depends on index and reranker)
        await component_initializer.get_query_engine()

        # Store references in app.state for backward compatibility
        app.state.llm = component_initializer._llm
        app.state.embed_model = component_initializer._embed_model
        app.state.reranker = component_initializer._reranker
        app.state.sharepoint_client = component_initializer._sharepoint_client
        app.state.index = component_initializer._index
        app.state.query_engine = component_initializer._query_engine

        total_time = time.time() - start_time
        logger.info(f"Optimized initialization completed in {total_time:.2f}s")
        logger.info(f"Component status: {component_initializer.get_status()}")

    except Exception as e:
        logger.error(f"Error during optimized initialization: {e}", exc_info=True)


async def create_empty_index():
    """Create an empty index without documents."""
    try:
        logger.info("Creating new empty index...")
        # Use Settings instead of ServiceContext
        llm = LlamaOpenAI(
            model=OPENAI_MODEL,
            temperature=0.7,
            max_tokens=2000,
        )
        embed_model = OpenAIEmbedding(model="text-embedding-3-small")

        LlamaSettings.llm = llm
        LlamaSettings.embed_model = embed_model

        # Create an empty vector store index
        index = VectorStoreIndex([])

        # Persist the empty index to storage
        storage_context = index.storage_context
        storage_context.persist(persist_dir=str(settings.STORAGE_DIR))
        logger.info("Successfully created and persisted empty index")

        return index
    except Exception as e:
        logger.error(f"Error creating empty index: {str(e)}")
        # Return a non-persisted index as fallback
        logger.warning("Creating non-persisted fallback index")
        return VectorStoreIndex([])


def get_query_engine(index, reranker=None):
    """Create a query engine from the index and reranker."""
    try:
        logger.info("Setting up query engine...")
        # Create retriever with increased similarity_top_k
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=10,  # Increased from default
        )
        logger.info("Created vector index retriever")

        # Create response synthesizer with more detailed configuration
        response_synthesizer = get_response_synthesizer(
            response_mode="compact",
            verbose=True,
            streaming=False,
        )
        logger.info("Created response synthesizer")

        # Create query engine
        query_engine = RetrieverQueryEngine(
            retriever=retriever,
            response_synthesizer=response_synthesizer,
            node_postprocessors=[reranker] if reranker else [],
        )
        logger.info("Query engine created successfully")
        return query_engine
    except Exception as e:
        logger.error(f"Error creating query engine: {str(e)}")
        return None


# Function to load or create the vector index
async def load_index():
    """Load the vector index from storage or create a new one if it doesn't exist."""
    logger.info("Attempting to load index from local storage...")
    storage_dir = settings.STORAGE_DIR
    try:
        if storage_dir.exists() and any(storage_dir.iterdir()):
            storage_context = StorageContext.from_defaults(persist_dir=str(storage_dir))
            index = load_index_from_storage(storage_context)
            logger.info("Index loaded successfully from storage")
            return index
        else:
            logger.warning("No existing index found; creating empty index")
            return await create_empty_index()
    except Exception as e:
        logger.error(f"Error loading index: {e}", exc_info=True)
        logger.warning("Falling back to creating empty index")
        return await create_empty_index()


async def persist_index():
    """Persists the current index state to disk using thread-safe operations."""
    if hasattr(app.state, "index") and hasattr(app.state.index, "storage_context"):
        try:
            logger.info("Persisting index changes to storage...")
            await safe_index_ops.safe_persist_index(app.state.index, str(settings.STORAGE_DIR))
            logger.info("Index changes persisted successfully.")
        except Exception as e:
            logger.error(f"Error persisting index changes: {e}", exc_info=True)
    else:
        logger.warning("Index or storage context not available, cannot persist.")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup resources on application shutdown."""
    if hasattr(app.state, "index"):
        try:
            logger.info("Persisting index to storage...")
            await persist_index()
            logger.info("Index persisted successfully")
        except Exception as e:
            logger.error(f"Error persisting index: {e}")

    # --- Optional: Shutdown scheduler and delete subscription ---
    if hasattr(app.state, "scheduler") and app.state.scheduler.running:
        logger.info("Shutting down webhook renewal scheduler...")
        app.state.scheduler.shutdown()

    if (
        hasattr(app.state, "sharepoint_subscription_id")
        and app.state.sharepoint_subscription_id
        and hasattr(app.state, "sharepoint_client")
    ):
        logger.info(
            f"Attempting to delete webhook subscription: {app.state.sharepoint_subscription_id}"
        )
        app_token = await get_application_token()
        if app_token:
            try:
                await app.state.sharepoint_client.delete_webhook_subscription(
                    app.state.sharepoint_subscription_id, app_token
                )
                logger.info("Successfully deleted webhook subscription.")
            except Exception as e:
                logger.error(f"Failed to delete webhook subscription on shutdown: {e}")
        else:
            logger.warning(
                "Could not get application token to delete webhook subscription on shutdown."
            )
    
    # Cleanup global resources
    try:
        from resource_management import cleanup_resources
        from rate_limiting import cleanup_rate_limiter
        
        await cleanup_resources()
        await cleanup_rate_limiter()
        logger.info("Successfully cleaned up application resources")
    except Exception as e:
        logger.error(f"Error during resource cleanup: {e}")


# --- Basic Auth Endpoints and Helpers ---
async def get_microsoft_access_token(request: Request) -> str:
    """Get Microsoft Graph API access token from session using secure token management."""
    session = request.session
    
    # Validate session first
    if not session_manager.validate_session(session):
        logger.info("Invalid session detected, redirecting to login")
        if request.url.path.startswith("/api/"):
            raise HTTPException(
                status_code=401, detail="Microsoft authentication required"
            )
        session["post_auth_redirect_url"] = str(request.url)
        return RedirectResponse(url=request.url_for("login_microsoft"), status_code=302)

    # Try to get decrypted token from session first
    access_token = session_manager.get_decrypted_token(session)
    if access_token:
        logger.debug("Using valid token from secure session cache")
        return access_token

    # Token not available or expired, try silent acquisition
    try:
        logger.info("Attempting silent token acquisition")
        accounts = msal_app.get_accounts()
        account = accounts[0] if accounts else None
        
        result = await asyncio.wait_for(
            asyncio.to_thread(msal_app.acquire_token_silent, MSAL_SCOPES, account),
            timeout=10,
        )
        
        if isinstance(result, dict) and "access_token" in result:
            # Update session with new encrypted token
            session_data = session_manager.create_microsoft_session_data(
                token_result=result,
                email=session.get("ms_user", {}).get("email", ""),
                name=session.get("ms_user", {}).get("name", "")
            )
            
            # Update only the token cache part of the session
            session.update(session_data)
            
            access_token = result["access_token"]
            logger.info("Silent token acquisition successful, session updated")
            return access_token
        else:
            logger.warning("Silent token acquisition failed")
            
    except Exception as e:
        logger.error(f"Error during silent token acquisition: {e}")
        if request.url.path.startswith("/api/"):
            raise HTTPException(
                status_code=502, detail="Could not reach Azure AD token endpoint"
            )

    # Silent acquisition failed: redirect to login
    logger.info("Redirecting to login due to token acquisition failure")
    if request.url.path.startswith("/api/"):
        raise HTTPException(
            status_code=401, detail="Microsoft authentication required"
        )
    
    session["post_auth_redirect_url"] = str(request.url)
    return RedirectResponse(url=request.url_for("login_microsoft"), status_code=302)


MsTokenDep = Depends(get_microsoft_access_token)


# --- Basic Auth Endpoints ---
@app.post("/login")
async def login(
    request: Request, 
    credentials: HTTPBasicCredentials = Depends(security),
    csrf_valid: bool = CSRFProtect
):
    # Verify credentials against settings (environment variables)
    correct_username = settings.USERNAME
    correct_password = settings.PASSWORD.get_secret_value()

    # Simple credential validation
    if not (
        credentials.username == correct_username
        and credentials.password == correct_password
    ):
        logger.warning(f"Failed login attempt for user: {credentials.username}")
        raise HTTPException(
            status_code=401,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )

    # --- Clear previous session data before setting new ---
    session = request.session
    session.clear()
    # --------------------------------------------------------

    # Set user in session (only Basic Auth info)
    session["basic_user"] = {
        "username": credentials.username,
        "authenticated": True,
    }
    # Optionally set the general 'authenticated' flag if needed by other parts
    session["authenticated"] = True
    session["auth_method"] = "basic"

    logger.info(f"User '{credentials.username}' successfully logged in via Basic Auth")

    # Return success
    return {"authenticated": True, "username": credentials.username}


# New login endpoint
@app.get("/login")
async def login_page():
    """Display login page with Microsoft login button."""
    return HTMLResponse(
        """
        <div style="font-family: Arial, sans-serif; max-width: 400px; margin: 100px auto; padding: 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); text-align: center;">
            <img src="static/ddb-logo-login.png" alt="DDB Logo" style="width: 120px; margin: 0 auto 1.5rem; display: block;" onerror="this.onerror=null; this.src='static/ddb-logo-80x80.png';">
            <h1 style="font-size: 1.5rem; margin-bottom: 2rem; color: #ffd100;">DDBrain</h1>
            <a href="/login/microsoft" style="display: block; background-color: #0078d4; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold; margin-bottom: 1rem;">
                Sign in with Microsoft
            </a>
            <a href="/auth/basic" style="display: block; background-color: #6c757d; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                Sign in with Basic Auth
            </a>
        </div>
        """
    )


@app.get("/auth/basic")
async def basic_auth_page():
    """Display basic authentication form."""
    return HTMLResponse(
        """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Basic Authentication - DDBrain</title>
            <style>
                body { font-family: Arial, sans-serif; background-color: #f5f5f5; margin: 0; padding: 20px; }
                .container { max-width: 400px; margin: 100px auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
                .logo { width: 120px; margin: 0 auto 1.5rem; display: block; }
                h1 { text-align: center; color: #ffd100; margin-bottom: 2rem; }
                .form-group { margin-bottom: 1rem; }
                label { display: block; margin-bottom: 0.5rem; font-weight: bold; }
                input[type="text"], input[type="password"] { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
                .btn { width: 100%; background-color: #0078d4; color: white; padding: 12px; border: none; border-radius: 4px; font-weight: bold; cursor: pointer; }
                .btn:hover { background-color: #106ebe; }
                .back-link { text-align: center; margin-top: 1rem; }
                .back-link a { color: #6c757d; text-decoration: none; }
            </style>
        </head>
        <body>
            <div class="container">
                <img src="/static/ddb-logo-login.png" alt="DDB Logo" class="logo" onerror="this.onerror=null; this.src='/static/ddb-logo-80x80.png';">
                <h1>DDBrain</h1>
                <form method="post" action="/auth/basic">
                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn">Sign In</button>
                </form>
                <div class="back-link">
                    <a href="/login">← Back to login options</a>
                </div>
            </div>
        </body>
        </html>
        """
    )


@app.post("/auth/basic")
async def basic_auth_login(request: Request, username: str = Form(...), password: str = Form(...)):
    """Handle basic authentication login."""
    try:
        # Check credentials against environment variables
        if username == settings.USERNAME and password == settings.PASSWORD:
            # Create basic auth session
            session = request.session
            session["authenticated"] = True
            session["auth_method"] = "basic"
            session["basic_user"] = {
                "authenticated": True,
                "username": username,
                "auth_timestamp": time.time()
            }

            logger.info(f"Basic auth successful for user: {username}")

            # Redirect to main page
            return RedirectResponse(url="/?login_success=true", status_code=302)
        else:
            logger.warning(f"Basic auth failed for user: {username}")
            return HTMLResponse(
                """
                <div style="font-family: Arial, sans-serif; max-width: 400px; margin: 100px auto; padding: 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); text-align: center; background-color: #f8d7da; border: 1px solid #f5c6cb;">
                    <h2 style="color: #721c24;">Authentication Failed</h2>
                    <p>Invalid username or password.</p>
                    <a href="/auth/basic" style="color: #0078d4;">Try again</a>
                </div>
                """,
                status_code=401
            )
    except Exception as e:
        logger.error(f"Error during basic auth: {e}")
        return HTMLResponse(
            """
            <div style="font-family: Arial, sans-serif; max-width: 400px; margin: 100px auto; padding: 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); text-align: center; background-color: #f8d7da; border: 1px solid #f5c6cb;">
                <h2 style="color: #721c24;">Authentication Error</h2>
                <p>An error occurred during authentication.</p>
                <a href="/auth/basic" style="color: #0078d4;">Try again</a>
            </div>
            """,
            status_code=500
        )


@app.post("/logout")
async def logout(request: Request, csrf_valid: bool = CSRFProtect):
    # Clear the session
    session = request.session
    session.clear()
    logger.info("User logged out")
    return {"message": "Successfully logged out"}


@app.get("/user")
async def get_user_status(request: Request):
    # Get user from session or return unauthenticated
    session = request.session
    # <<< Log session content for debugging >>>
    logger.info(f"[/user] Session content: {dict(session)}")
    logger.info(f"[/user] Session keys: {list(session.keys())}")
    user = _extract_user(session)
    if user:
        logger.info(f"[/user] Found user in session: {user}")
        user["is_admin"] = user_is_admin(session)
        return user
    logger.info("[/user] No user found in session.")
    return {"authenticated": False, "is_admin": False}


@app.get("/login/microsoft", name="login_microsoft")
async def login_microsoft(request: Request, login_hint: Optional[str] = Query(None)):
    """Initiate Microsoft OAuth flow, optionally using a login hint."""
    try:
        if not msal_app:
            logger.error("Microsoft authentication is not configured")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Microsoft Authentication Not Configured",
                    "error_message": "Microsoft authentication is not properly configured.",
                    "error_details": "Please check your environment variables: MS_CLIENT_ID, MS_CLIENT_SECRET, and MS_TENANT_ID",
                },
                status_code=500,
            )

        # Generate state for CSRF protection
        session = request.session
        state = str(uuid.uuid4())
        session["ms_auth_state"] = state
        logger.info(f"Generated MS auth state: {state}")
        if login_hint:
            logger.info(f"Received login_hint: {login_hint}")
        else:
            logger.info("No login_hint received.")

        # Generate the authorization URL, passing the hint if present
        auth_uri = msal_app.get_authorization_request_url(
            MSAL_SCOPES,
            state=state,
            redirect_uri=EXPECTED_REDIRECT,  # Use the correctly constructed URI
            login_hint=login_hint,  # <<< Pass the hint here
        )

        # Log the generated URL before redirecting
        logger.info(f"Generated Microsoft auth URL for redirect: {auth_uri}")

        # Return the redirect response
        # Use 303 See Other for POST-like behavior after GET
        return RedirectResponse(auth_uri, status_code=303)

    except Exception as e:
        logger.error(f"Error during Microsoft login initiation: {e}", exc_info=True)
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Microsoft Login Error",
                "error_message": "Failed to initiate Microsoft login.",
                "error_details": f"Error details: {str(e)}",
            },
            status_code=500,
        )


@app.get("/auth/callback")
async def auth_callback(
    request: Request,
    code: str = None,
    state: str = None,
    error: str = None,
    error_description: str = None,
):
    """Handle the Microsoft OAuth callback with secure session management."""
    try:
        session = request.session
        logger.info("[/auth/callback] Starting OAuth callback processing")

        # Handle potential errors from Microsoft
        if error:
            logger.error(f"Microsoft OAuth error: {error} - {error_description}")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Microsoft Authentication Error",
                    "error_message": f"Error: {error}",
                    "error_details": error_description or "No details provided",
                },
                status_code=400,
            )

        # Verify state parameter to prevent CSRF attacks
        expected_state = session.get("ms_auth_state")
        if not state or expected_state != state:
            logger.error(f"State mismatch. Expected: {expected_state}, Received: {state}")
            # Clear potentially compromised session
            session_manager.clear_session_safely(session)
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Authentication Error",
                    "error_message": "Invalid authentication state.",
                    "error_details": "The state parameter did not match. This could indicate a cross-site request forgery attempt.",
                },
                status_code=400,
            )

        # Process the callback with authorization code
        if not code:
            logger.error("No authorization code provided in callback")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Authentication Error",
                    "error_message": "No authorization code provided.",
                    "error_details": "The OAuth callback did not contain an authorization code.",
                },
                status_code=400,
            )

        try:
            # Store redirect target before any session modifications
            redirect_target = session.get("post_auth_redirect_url")
            logger.info(f"Preserved redirect target: {redirect_target}")

            # Exchange code for token in a background thread with timeout
            result = await asyncio.wait_for(
                asyncio.to_thread(
                    msal_app.acquire_token_by_authorization_code,
                    code,
                    MSAL_SCOPES,
                    EXPECTED_REDIRECT,
                ),
                timeout=60,
            )

            if "error" in result:
                logger.error(
                    f"Token acquisition error: {result.get('error')} - {result.get('error_description')}"
                )
                return templates.TemplateResponse(
                    "error.html",
                    {
                        "request": request,
                        "error_title": "Token Acquisition Error",
                        "error_message": f"Error: {result.get('error')}",
                        "error_details": result.get(
                            "error_description", "No details provided"
                        ),
                    },
                    status_code=400,
                )

            # Successfully acquired token
            logger.info("Token acquired successfully")

            # Get token claims and user info
            id_token_claims = result.get("id_token_claims", {})
            username = id_token_claims.get("preferred_username", "unknown")
            email = id_token_claims.get("email", username)
            name = id_token_claims.get("name", "User")

            logger.info(f"Microsoft login successful for {email}")

            # Create secure session data with encrypted tokens
            session_data = session_manager.create_microsoft_session_data(
                token_result=result,
                email=email,
                name=name
            )

            # Atomically update session with new authentication data
            update_success = session_manager.atomic_session_update(
                session=session,
                updates=session_data,
                preserve_keys=["post_auth_redirect_url"] if redirect_target else None
            )

            if not update_success:
                logger.error("Failed to update session atomically")
                return templates.TemplateResponse(
                    "error.html",
                    {
                        "request": request,
                        "error_title": "Session Error",
                        "error_message": "Failed to establish secure session.",
                        "error_details": "Please try logging in again.",
                    },
                    status_code=500,
                )

            # Clear authentication state after successful login
            session.pop("ms_auth_state", None)
            
            # Determine redirect URL
            final_redirect = session.pop("post_auth_redirect_url", None) or redirect_target
            if not final_redirect:
                redirect_url = "/?login_success=true"
                logger.info("No redirect target, using homepage")
            else:
                redirect_url = final_redirect
                # Add login success flag
                separator = "&" if "?" in redirect_url else "?"
                redirect_url += f"{separator}login_success=true"
                logger.info(f"Redirecting to: {redirect_url}")

            # Validate final session state
            if not session_manager.validate_session(session):
                logger.error("Session validation failed after update")
                session_manager.clear_session_safely(session)
                return templates.TemplateResponse(
                    "error.html",
                    {
                        "request": request,
                        "error_title": "Session Error",
                        "error_message": "Session validation failed.",
                        "error_details": "Please try logging in again.",
                    },
                    status_code=500,
                )

            logger.info("Authentication callback completed successfully")
            return RedirectResponse(url=redirect_url, status_code=303)

        except asyncio.TimeoutError:
            logger.error("Token acquisition timed out")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Authentication Timeout",
                    "error_message": "Authentication request timed out.",
                    "error_details": "Please try logging in again.",
                },
                status_code=408,
            )

    except Exception as e:
        logger.error(f"Error during OAuth callback: {e}", exc_info=True)
        # Clear session on unexpected errors
        session_manager.clear_session_safely(session)
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Authentication Error",
                "error_message": "An unexpected error occurred during authentication.",
                "error_details": "Please try logging in again.",
            },
            status_code=500,
        )


@app.get("/sharepoint/sites", name="list_sharepoint_sites")
async def list_sharepoint_sites_view(request: Request):
    """Render the SharePoint sites view for the authenticated user."""
    # <<< Add early entry log >>>
    logger.info("Entering list_sharepoint_sites_view")
    # <<< End early entry log >>>
    try:
        # <<< Log session content on entry >>>
        logger.info(
            f"[/sharepoint/sites] Session content on entry: {dict(request.session)}"
        )
        # <<< End log >>>

        logger.info("Starting list_sharepoint_sites_view endpoint")
        logger.info(f"Session content at /sharepoint/sites: {dict(request.session)}")

        # First verify SharePoint client is initialized
        if not hasattr(app.state, "sharepoint_client"):
            logger.error("SharePoint client is not initialized.")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "SharePoint Not Configured",
                    "error_message": "SharePoint integration is not configured or failed to initialize.",
                    "error_details": "Check your server configuration and environment variables.",
                },
                status_code=500,
            )

        # Check for authentication
        session = request.session
        is_authenticated = False
        if "basic_user" in session and session["basic_user"].get("authenticated"):
            is_authenticated = True
        elif "ms_user" in session:
            is_authenticated = True

        if not is_authenticated:
            logger.warning(
                f"No authentication found in session. Session keys: {list(session.keys())}"
            )
            # <<< Store target URL before redirecting >>>
            session["post_auth_redirect_url"] = str(request.url)
            logger.info(
                f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
            )
            # <<< End store >>>
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        # Get the access token
        if "ms_token_cache" not in session:
            logger.warning(
                "No Microsoft token cache found in session, redirecting to login"
            )
            # <<< Store target URL before redirecting >>>
            session["post_auth_redirect_url"] = str(request.url)
            logger.info(
                f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
            )
            # <<< End store >>>
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        cached_token = session.get("ms_token_cache", {})
        access_token = cached_token.get("access_token")

        if not access_token:
            logger.warning(
                "No valid access token found in token cache, redirecting to login"
            )
            # Clear any invalid session data
            if "ms_token_cache" in session:
                del session["ms_token_cache"]
            if "ms_user" in session:
                del session["ms_user"]
            # <<< Store target URL before redirecting >>>
            session["post_auth_redirect_url"] = str(request.url)
            logger.info(
                f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
            )
            # <<< End store >>>
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        try:
            # List SharePoint sites
            logger.info("Calling SharePoint client to list sites")
            try:
                sites = await asyncio.wait_for(
                    app.state.sharepoint_client.list_sites(access_token), timeout=10
                )
            except asyncio.TimeoutError:
                logger.error("Timeout listing SharePoint sites")
                raise HTTPException(status_code=504, detail="Timeout listing sites")

            # Log the results for debugging
            site_count = len(sites) if sites else 0
            logger.info(f"Found {site_count} SharePoint sites")

            # Store the first site ID in the session (assuming it's the one we want)
            if sites and len(sites) > 0:
                session["current_site_id"] = sites[0].get("id", "")
                logger.info(f"Stored site ID in session: {session['current_site_id']}")

            return templates.TemplateResponse(
                "sharepoint_sites.html", {"request": request, "sites": sites}
            )
        except Exception as e:
            error_message = str(e).lower()
            # Check for any token-related errors
            if any(
                keyword in error_message
                for keyword in ["token", "401", "unauthorized", "expired"]
            ):
                logger.info("Token expired or invalid, redirecting to Microsoft login")
                # Clear the expired token
                if "ms_token_cache" in session:
                    del session["ms_token_cache"]
                if "ms_user" in session:
                    del session["ms_user"]
                redirect_url = request.url_for("login_microsoft")
                return RedirectResponse(url=str(redirect_url), status_code=302)
            raise  # Re-raise other exceptions

    except Exception as e:
        logger.error(f"Error in list_sharepoint_sites_view: {e}", exc_info=True)
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "SharePoint Error",
                "error_message": str(e),
                "error_details": f"Exception type: {type(e).__name__}",
            },
            status_code=500,
        )


@app.get("/sharepoint/drives/{site_id}", name="list_sharepoint_drives")
async def list_sharepoint_drives(
    request: Request, site_id: str, user=CurrentUser, ms_token: str = MsTokenDep
):
    """List document libraries (drives) within a SharePoint site."""
    if not hasattr(app.state, "sharepoint_client"):
        logger.error("SharePoint client is not initialized.")
        raise HTTPException(
            status_code=500,
            detail="SharePoint integration is not configured or failed to initialize.",
        )

    try:
        # Validate SharePoint site ID
        try:
            validated_site_id = validator.validate_sharepoint_site_id(site_id)
        except ValidationError as e:
            logger.warning(f"Invalid site ID provided: {validator.sanitize_for_logging(site_id)}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid site ID: {str(e)}"
            )

        logger.info(f"Listing drives for validated site ID: {validator.sanitize_for_logging(validated_site_id)}")
        logger.info(f"Token length: {len(ms_token) if ms_token else 0} characters")

        # --- MODIFICATION START ---
        # Define the target site ID components and repository path
        # Site ID format: {hostname},{siteCollectionId},{siteId}
        target_hostname = "ddbgroupcomph-my.sharepoint.com"
        target_site_collection_id = "c05ba4f2-c263-4c53-a6be-e3e094c754d4"
        target_site_id_component = "40d66325-d5a1-4aa6-b0f4-0caa6705ed2d"
        target_full_site_id = (
            f"{target_hostname},{target_site_collection_id},{target_site_id_component}"
        )
        # --- Use drive-relative path (try root level) ---
        target_repo_path = "DDB Group Repository"
        # --- End path change ---

        # Check if the requested site_id matches the target IT Storage site ID
        if validated_site_id == target_full_site_id:
            logger.info(
                f"Detected target IT Storage site ID: {validator.sanitize_for_logging(validated_site_id)}. Attempting to get its primary drive and redirect."
            )
            try:
                # Get drives specifically for the target site
                drives = await asyncio.wait_for(
                    app.state.sharepoint_client.list_drives(
                        site_id=validated_site_id, token=ms_token
                    ),
                    timeout=10,
                )

                if drives:
                    # Assume the first drive is the primary one for a personal site
                    target_drive = drives[0]
                    target_drive_id = target_drive.get("id")
                    logger.info(
                        f"Found primary drive ID for IT Storage site: {target_drive_id}"
                    )

                    if target_drive_id:
                        try:
                            # Construct URL manually to avoid issues with url_for and query params
                            base_url_path = request.url_for(
                                "list_sharepoint_files", drive_id=target_drive_id
                            )
                            # URL-encode the folder path for the query string
                            encoded_folder_path = quote(target_repo_path, safe="")
                            redirect_url = (
                                f"{base_url_path}?folder_path={encoded_folder_path}"
                            )

                            logger.info(
                                f"Redirecting to IT Storage DDB Group Repository (manual URL): {redirect_url}"
                            )
                            return RedirectResponse(
                                url=str(redirect_url), status_code=302
                            )
                        except NoMatchFound:  # This except block should now work
                            logger.error(
                                f"Could not generate BASE URL for list_sharepoint_files (drive_id: {target_drive_id}). Route misconfigured?",
                                exc_info=True,
                            )
                            # Fall through to standard behavior if base URL generation fails
                        except Exception as url_err:  # Catch other potential errors during manual URL construction
                            logger.error(
                                f"Error constructing redirect URL: {url_err}",
                                exc_info=True,
                            )
                            # Fall through
                    else:
                        logger.warning(
                            "Could not extract drive ID from the drives list for IT Storage site."
                        )
                else:
                    logger.warning(
                        f"No drives found for the target IT Storage site ID: {site_id}"
                    )

            except asyncio.TimeoutError:
                logger.error(
                    f"Timeout listing drives for target IT Storage site {site_id}"
                )
                # Fall through to standard error handling
            except Exception as e:
                logger.error(
                    f"Error processing target IT Storage site {site_id}: {e}",
                    exc_info=True,
                )
                # Fall through to standard error handling

            # If redirect failed for any reason, fall through to rendering the drive list page for IT Storage
            logger.warning(
                f"Redirect failed for IT Storage site {site_id}. Falling back to rendering drive list."
            )
            # Note: 'drives' might be populated here from the attempt above
            if (
                "drives" not in locals()
            ):  # Ensure drives is defined if list_drives failed above
                drives = []
            return templates.TemplateResponse(
                "sharepoint_drives.html",
                {"request": request, "drives": drives, "site_id": validated_site_id},
            )
        else:
            logger.info(
                f"Requested site_id does not match target IT Storage site. Proceeding with standard drive listing."
            )
        # --- MODIFICATION END ---

        # Standard approach: List drives for non-target sites
        logger.info(f"Proceeding with standard drive listing for validated site_id")
        try:
            drives = await asyncio.wait_for(
                app.state.sharepoint_client.list_drives(
                    site_id=validated_site_id, token=ms_token
                ),
                timeout=10,
            )
        except asyncio.TimeoutError:
            logger.error(f"Timeout listing drives for site")
            raise HTTPException(status_code=504, detail="Timeout listing drives")
        logger.info(f"Found {len(drives)} drives for site")

        return templates.TemplateResponse(
            "sharepoint_drives.html",
            {"request": request, "drives": drives, "site_id": validated_site_id},
        )
    except Exception as e:
        logger.error(f"Error listing drives for site: {e}", exc_info=True)

        # Check if the error is likely due to token issues (e.g., Graph API errors)
        error_detail = f"Failed to list document libraries: {e}"
        status_code = 500

        if "401" in str(e) or "Unauthorized" in str(e):
            status_code = 401
            error_detail = "Failed to list document libraries: Authentication failed or token expired. Please try logging out and back in via Microsoft."

        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "SharePoint Error",
                "error_message": error_detail,
                "error_details": f"Exception type: {type(e).__name__}, Message: {str(e)}",
            },
            status_code=status_code,
        )


@app.get("/sharepoint/files/{drive_id}", name="list_sharepoint_files")
async def list_sharepoint_files(
    request: Request,
    drive_id: str,
    folder_path: str = "",
    user=CurrentUser,
    ms_token: str = MsTokenDep,
):
    """List files and folders in a SharePoint drive or folder."""
    if not hasattr(app.state, "sharepoint_client"):
        logger.error("SharePoint client is not initialized.")
        raise HTTPException(
            status_code=500,
            detail="SharePoint integration is not configured or failed to initialize.",
        )

    try:
        # Validate inputs
        try:
            validated_drive_id = validator.validate_sharepoint_id(drive_id)
            validated_folder_path = validator.validate_folder_path(folder_path)
        except ValidationError as e:
            logger.warning(f"Invalid input provided: {validator.sanitize_for_logging(f'drive_id={drive_id}, folder_path={folder_path}')}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid input: {str(e)}"
            )

        logger.info(
            f"Listing files for validated drive ID: {validator.sanitize_for_logging(validated_drive_id)}, folder path: {validator.sanitize_for_logging(validated_folder_path)}"
        )
        logger.info(f"Token length: {len(ms_token) if ms_token else 0} characters")

        # Get the files from SharePoint
        try:
            files = await asyncio.wait_for(
                app.state.sharepoint_client.list_files(
                    drive_id=validated_drive_id, token=ms_token, folder_path=validated_folder_path
                ),
                timeout=10,
            )
        except asyncio.TimeoutError:
            logger.error(f"Timeout listing files for drive")
            raise HTTPException(status_code=504, detail="Timeout listing files")
        logger.info(f"Found {len(files)} files/folders")

        # Get the site ID from the session or a default value
        site_id = request.session.get("current_site_id", "")

        return templates.TemplateResponse(
            "sharepoint_files.html",
            {
                "request": request,
                "files": files,
                "drive_id": drive_id,
                "current_path": folder_path,
                "site_id": site_id,
            },
        )
    except Exception as e:
        logger.error(f"Error listing files for drive {drive_id}: {e}", exc_info=True)

        # Check if the error is likely due to token issues
        error_detail = f"Failed to list files: {e}"
        status_code = 500

        if "401" in str(e) or "Unauthorized" in str(e):
            status_code = 401
            error_detail = "Failed to list files: Authentication failed or token expired. Please try logging out and back in via Microsoft."

        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "SharePoint Error",
                "error_message": error_detail,
                "error_details": f"Exception type: {type(e).__name__}, Message: {str(e)}",
            },
            status_code=status_code,
        )


async def add_document_to_index(file_path: str, metadata: dict):
    """Loads a document from file_path, adds metadata, and inserts into the index."""
    try:
        file_name_for_log = metadata.get("file_name", Path(file_path).name)
        logger.info(
            f"Attempting to index document: {file_path} (File: {file_name_for_log})"
        )

        if not hasattr(app.state, "index") or not app.state.index:
            logger.error(f"Index not available, cannot add document: {file_path}")
            return

        # Check for existing document with same SharePoint ID using safe operations
        sharepoint_id = metadata.get("sharepoint_id")
        if sharepoint_id and hasattr(app.state.index, "docstore"):
            # Find documents with the same SharePoint ID
            existing_docs = await safe_index_ops.safe_search_docstore(
                app.state.index, {"sharepoint_id": sharepoint_id}
            )
            
            # Delete any found duplicates using bulk delete
            if existing_docs:
                deleted_count = await safe_index_ops.safe_bulk_delete(app.state.index, existing_docs)
                logger.info(
                    f"Removed {deleted_count} existing documents with SharePoint ID {sharepoint_id}"
                )

        # Use SimpleDirectoryReader to load the document with enhanced error handling
        try:
            reader = SimpleDirectoryReader(input_files=[file_path])
            documents = await asyncio.to_thread(reader.load_data)
        except Exception as reader_error:
            # Handle specific timestamp-related errors and other file processing issues
            error_msg = str(reader_error).lower()
            if "timestamp" in error_msg or "nonetype" in error_msg:
                logger.warning(f"File metadata issue detected for {file_path}: {reader_error}")
                # Try to create a minimal document with basic content extraction
                try:
                    from llama_index.core.schema import Document
                    # Attempt basic text extraction without metadata
                    file_content = ""
                    file_path_obj = Path(file_path)

                    if file_path_obj.suffix.lower() == '.txt':
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            file_content = f.read()
                    elif file_path_obj.suffix.lower() == '.pdf':
                        # Basic PDF text extraction fallback
                        try:
                            import PyPDF2
                            with open(file_path, 'rb') as f:
                                pdf_reader = PyPDF2.PdfReader(f)
                                file_content = "\n".join([page.extract_text() for page in pdf_reader.pages])
                        except Exception:
                            file_content = f"[PDF content could not be extracted from {file_path_obj.name}]"
                    else:
                        file_content = f"[Content extraction not supported for {file_path_obj.suffix} files]"

                    # Create a basic document with minimal metadata
                    documents = [Document(
                        text=file_content,
                        metadata={
                            "file_name": file_path_obj.name,
                            "file_path": str(file_path),
                            "extraction_method": "fallback_due_to_metadata_error"
                        }
                    )]
                    logger.info(f"Created fallback document for {file_path} due to metadata processing error")
                except Exception as fallback_error:
                    logger.error(f"Fallback document creation also failed for {file_path}: {fallback_error}")
                    return
            else:
                # Re-raise other types of errors
                logger.error(f"Document reader error for {file_path}: {reader_error}")
                raise

        if not documents:
            logger.warning(f"No content extracted from document: {file_path}")
            return

        nodes_to_insert = []
        for doc in documents:
            # Combine existing doc metadata with SharePoint metadata
            combined_metadata = {**doc.metadata, **metadata}

            # Ensure required metadata fields
            combined_metadata.setdefault(
                "file_name", metadata.get("file_name", Path(file_path).name)
            )
            combined_metadata.setdefault("web_url", metadata.get("web_url", ""))
            doc.metadata = combined_metadata

            # Create TextNode for insertion
            node = TextNode(
                text=doc.get_content(metadata_mode=MetadataMode.NONE),
                metadata=doc.metadata,
            )
            nodes_to_insert.append(node)

        if nodes_to_insert:
            # Insert nodes into the index using safe operations
            await safe_index_ops.safe_insert_nodes(app.state.index, nodes_to_insert)
            # Persist changes
            await persist_index()
            logger.info(
                f"Successfully added/updated {len(nodes_to_insert)} nodes in index from: {file_name_for_log}"
            )
        else:
            logger.info(f"No nodes generated for indexing from: {file_name_for_log}")

    except FileNotFoundError as e:
        logger.error(f"File not found when indexing {metadata.get('file_name', file_path)}: {e}")
        raise HTTPException(status_code=404, detail=f"File not found: {metadata.get('file_name', 'Unknown')}")
    except PermissionError as e:
        logger.error(f"Permission denied when accessing {metadata.get('file_name', file_path)}: {e}")
        raise HTTPException(status_code=403, detail=f"Permission denied accessing file: {metadata.get('file_name', 'Unknown')}")
    except OSError as e:
        logger.error(f"OS error when processing {metadata.get('file_name', file_path)}: {e}")
        raise HTTPException(status_code=500, detail=f"System error processing file: {metadata.get('file_name', 'Unknown')}")
    except ValueError as e:
        logger.error(f"Invalid file format or content in {metadata.get('file_name', file_path)}: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid file format: {metadata.get('file_name', 'Unknown')}")
    except Exception as e:
        error_type = type(e).__name__
        logger.error(
            f"Unexpected error ({error_type}) indexing document {metadata.get('file_name', file_path)}: {e}",
            exc_info=True,
        )
        # Don't re-raise for unexpected errors to prevent cascading failures
        # Instead, log the error and continue processing other files


async def import_sharepoint_item(request: Request, drive_id: str, item_id: str) -> dict:
    """Import a file or folder from SharePoint with enhanced batch processing and error handling."""
    from resource_management import resource_context
    
    async with resource_context.managed_operation(temp_dir_prefix="sharepoint_import_") as (temp_dir, session_mgr):
        try:
            access_token = await get_microsoft_access_token(request)
            item = await app.state.sharepoint_client.get_drive_item(
                drive_id, item_id, token=access_token
            )
            if not item:
                raise ValueError("Item not found")
            progress = {
                "total_files": 0,
                "processed_files": 0,
                "failed_files": 0,
                "skipped_files": [],
                "errors": [],
            }

            async def process_single_file(file_item, parent_path=""):
                file_path_for_error = file_item.get("name", "Unknown File")
                try:
                    # --- Check if document already exists in index ---
                    if (
                        hasattr(app.state, "index")
                        and app.state.index
                        and hasattr(app.state.index, "docstore")
                    ):
                        existing_doc_id = None
                        target_sharepoint_id = file_item.get("id")
                    if target_sharepoint_id:
                        # Efficiently check if sharepoint_id exists in metadata
                        for doc_id, doc_info in app.state.index.docstore.docs.items():
                            if (
                                doc_info.metadata
                                and doc_info.metadata.get("sharepoint_id")
                                == target_sharepoint_id
                            ):
                                existing_doc_id = doc_id
                                break  # Found it

                    if existing_doc_id:
                        file_name_for_log = file_item.get("name", "Unknown File")
                        logger.info(
                            f"Skipping import for already indexed file: {file_name_for_log} (SharePoint ID: {target_sharepoint_id})"
                        )
                        progress["skipped_files"].append(
                            f"{file_item.get('name', 'Unknown File')} (already indexed)"
                        )
                        # Need to increment total_files here if we count skipped-as-existing towards total potential
                        # progress["total_files"] += 1 # Decide if this is desired
                        return  # Skip processing this file
                    # --- End check ---

                    file_name = file_item.get("name", "")
                    file_path = parent_path + "/" + file_name if parent_path else file_name
                    file_path_for_error = file_path
                    file_size = file_item.get("size", 0)
                    file_ext = Path(file_name).suffix.lower()

                    # Check file size limitation
                    if file_size > 100 * 1024 * 1024:
                        progress["skipped_files"].append(f"{file_path} (too large)")
                        return

                    # Check supported file types
                    if file_ext not in [
                        ".txt",
                        ".pdf",
                        ".doc",
                        ".docx",
                        ".rtf",
                        ".csv",
                        ".xlsx",
                        ".xls",
                        ".png",
                        ".jpg",
                        ".jpeg",
                        ".gif",
                        ".bmp",
                    ]:
                        progress["skipped_files"].append(f"{file_path} (unsupported type)")
                        return

                    # Download and process the file with managed cleanup
                    from resource_management import FileManager
                    temp_file_path = temp_dir / file_name

                    with FileManager.managed_path(temp_file_path, cleanup_on_error=True) as managed_path:
                        await app.state.sharepoint_client.download_file(
                            drive_id, file_item["id"], str(managed_path), access_token
                        )

                        # Special handling for specific problematic file
                        if "DDB_BrandGuidelines-2025.pdf" in str(managed_path):
                            logger.warning(f"Detected problematic PDF file: {managed_path}")
                            # Create a stub entry for the problematic PDF
                            metadata = {
                                "sharepoint_id": file_item["id"],
                                "created_datetime": file_item.get("createdDateTime"),
                                "last_modified_datetime": file_item.get(
                                    "lastModifiedDateTime"
                                ),
                                "created_by": file_item.get("createdBy", {})
                                .get("user", {})
                                .get("displayName"),
                                "web_url": file_item.get("webUrl"),
                                "parent_path": parent_path,
                                "is_stub": True,
                            }

                        # Create a TextNode directly with a stub message
                        stub_text = f"DDB Brand Guidelines (2025) - This document contains design and branding guidelines for DDB. Please access the original document on SharePoint for complete details."
                        node = TextNode(
                            text=stub_text,
                            metadata={**metadata, "file_name": file_name},
                        )
                        app.state.index.insert_nodes([node])
                        await persist_index()
                        progress["processed_files"] += 1
                        logger.info(f"Successfully created stub entry for {file_name}")
                        return

                    # Standard processing for other files
                    metadata = {
                        "sharepoint_id": file_item["id"],
                        "created_datetime": file_item.get("createdDateTime"),
                        "last_modified_datetime": file_item.get("lastModifiedDateTime"),
                        "created_by": file_item.get("createdBy", {})
                        .get("user", {})
                        .get("displayName"),
                        "web_url": file_item.get("webUrl"),
                        "parent_path": parent_path,
                    }
                    await add_document_to_index(str(managed_path), metadata)
                    progress["processed_files"] += 1
                    # File cleanup handled automatically by managed_path context
                except FileNotFoundError as e:
                    error_msg = f"File not found during processing {file_path_for_error}: {str(e)}"
                    logger.error(error_msg)
                    progress["failed_files"] += 1
                    progress["errors"].append(f"File not found: {file_path_for_error}")
                except PermissionError as e:
                    error_msg = f"Permission denied accessing {file_path_for_error}: {str(e)}"
                    logger.error(error_msg)
                    progress["failed_files"] += 1
                    progress["errors"].append(f"Permission denied: {file_path_for_error}")
                except ValueError as e:
                    error_msg = f"Invalid file format or content in {file_path_for_error}: {str(e)}"
                    logger.warning(error_msg)
                    progress["failed_files"] += 1
                    progress["errors"].append(f"Invalid file format: {file_path_for_error}")
                except ConnectionError as e:
                    error_msg = f"Network error downloading {file_path_for_error}: {str(e)}"
                    logger.error(error_msg)
                    progress["failed_files"] += 1
                    progress["errors"].append(f"Network error: {file_path_for_error}")
                except TimeoutError as e:
                    error_msg = f"Timeout processing {file_path_for_error}: {str(e)}"
                    logger.error(error_msg)
                    progress["failed_files"] += 1
                    progress["errors"].append(f"Timeout: {file_path_for_error}")
                except Exception as e:
                    error_type = type(e).__name__
                    error_msg = f"Unexpected error ({error_type}) processing {file_path_for_error}: {str(e)}"
                    logger.error(error_msg, exc_info=True)
                    progress["failed_files"] += 1
                    progress["errors"].append(f"Unexpected error ({error_type}): {file_path_for_error}")

            async def process_folder(folder_item, parent_path=""):
                folder_name_for_error = folder_item.get("name", "Unknown Folder")
                try:
                    folder_name = folder_item.get("name", "")
                    folder_name_for_error = (
                        f"{parent_path}/{folder_name}" if parent_path else folder_name
                    )
                    new_parent = (
                        parent_path + "/" + folder_name if parent_path else folder_name
                    )
                    logger.info(f"Processing folder: {new_parent}")
                    async for (
                        child
                    ) in app.state.sharepoint_client.list_folder_contents_recursive(
                        drive_id, folder_item["id"], token=access_token
                    ):
                        logger.debug(
                            f"Processing child item: Name={child.get('name')}, Keys={list(child.keys())}, IsFolder={child.get('folder')}, IsFile={child.get('file')}"
                        )
                        if "folder" in child:
                            await process_folder(child, new_parent)
                        else:
                            progress["total_files"] += 1
                            await process_single_file(child, new_parent)
                except Exception as e:
                    error_msg = f"Error processing folder {folder_name_for_error}: {str(e)}"
                    logger.error(error_msg, exc_info=True)
                    progress["errors"].append(error_msg)

            if "folder" in item:
                await process_folder(item)
            else:
                progress["total_files"] = 1
                await process_single_file(item)

            return {
                "status": "completed",
                "total_files": progress["total_files"],
                "processed_files": progress["processed_files"],
                "failed_files": progress["failed_files"],
                "skipped_files": progress["skipped_files"],
                "errors": progress["errors"],
            }

        except Exception as e:
            logger.error(f"Import failed: {str(e)}")
            return {"status": "failed", "error": str(e)}


@app.get(
    "/sharepoint/import/{drive_id}/{item_id}/{item_name}",
    name="import_sharepoint_item_route",
)
async def import_sharepoint_item_route(
    request: Request,
    drive_id: str,
    item_id: str,
    item_name: str,
    user=CurrentUser,
    ms_token: str = MsTokenDep,
    admin_user: str = AdminRequired,
):
    """
    Import a file or folder from SharePoint and render appropriate response.
    Requires Admin privileges.
    """

    try:
        # Log admin action
        action_desc = f"import SharePoint item '{item_name}' (ID: {item_id}) from drive: {drive_id}"
        log_admin_action(request.session, action_desc)
        
        # Call the import function, passing the request object
        result = await import_sharepoint_item(request, drive_id, item_id)

        if result["status"] == "completed":
            # Build success message
            message = f"Successfully processed {result['processed_files']} of {result['total_files']} files from '{item_name}'."
            details = []

            if result["skipped_files"]:
                details.append(f"Skipped files: {', '.join(result['skipped_files'])}")

            if result["failed_files"] > 0:
                details.append(f"Failed to process {result['failed_files']} files:")
                details.extend([f"  - {error}" for error in result["errors"]])

            # Log successful completion
            log_admin_action(request.session, f"successfully completed {action_desc}")

            return templates.TemplateResponse(
                "success.html",
                {
                    "request": request,
                    "message": message,
                    "details": "\n".join(details) if details else None,
                },
            )
        else:
            # Handle failed import
            error_msg = result.get("error", "Unknown error occurred")
            log_admin_action(request.session, action_desc, success=False, error=error_msg)
            
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Import Failed",
                    "error_message": f"Failed to import '{item_name}'",
                    "error_details": error_msg,
                },
                status_code=500,
            )

    except Exception as e:
        error_msg = str(e)
        log_admin_action(request.session, action_desc, success=False, error=error_msg)
        
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Import Error",
                "error_message": f"An unexpected error occurred while importing '{item_name}'",
                "error_details": error_msg,
            },
            status_code=500,
        )


# --- RAG Query Endpoints ---
@app.get("/query")
async def query_endpoint(
    request: Request,
    query: str = Query(..., min_length=1, max_length=500),
    user=CurrentUser,
):
    """Query the RAG system with a natural language question."""
    try:
        # Validate the query input
        try:
            validated_query = validator.validate_query_string(query)
        except ValidationError as e:
            logger.warning(f"Invalid query provided: {validator.sanitize_for_logging(query)}")
            return JSONResponse(
                status_code=400,
                content={
                    "error": "Invalid query format",
                    "details": str(e)
                }
            )
        
        logger.info(f"Processing validated query: {validator.sanitize_for_logging(validated_query)}")
        
        # Use validated query for processing
        # Check if query engine is available and initialize if needed
        if not hasattr(app.state, "query_engine") or not app.state.query_engine:
            logger.warning("Query engine not available - attempting to initialize")

            # Ensure index is loaded
            if not hasattr(app.state, "index") or not app.state.index:
                try:
                    logger.info("Index not found, attempting to load...")
                    app.state.index = await load_index()
                    logger.info("Index loaded successfully")
                except Exception as e:
                    logger.error(f"Failed to load index: {e}")
                    return JSONResponse(
                        status_code=500,
                        content={
                            "error": "Failed to load vector index",
                            "details": str(e),
                        },
                    )

            # Create query engine from index
            try:
                app.state.query_engine = get_query_engine(
                    app.state.index, app.state.reranker
                )
                logger.info("Query engine successfully initialized on-demand")
            except Exception as model_error:
                logger.error(
                    f"Error creating query engine with current model: {model_error}"
                )

                # Try with a more reliable fallback model
                try:
                    logger.info(
                        "Attempting to create query engine with fallback model..."
                    )
                    fallback_llm = LlamaOpenAI(
                        model="gpt-3.5-turbo",  # Fallback to a more widely available model
                        temperature=0.7,
                        max_tokens=1500,
                    )

                    # Update the global settings
                    LlamaSettings.llm = fallback_llm

                    # Try again with fallback model
                    app.state.query_engine = get_query_engine(
                        app.state.index, app.state.reranker
                    )
                    logger.info("Successfully created query engine with fallback model")
                except Exception as fallback_error:
                    logger.error(f"Fallback model also failed: {fallback_error}")
                    return JSONResponse(
                        status_code=500,
                        content={
                            "error": "Query engine initialization failed",
                            "details": f"Original error: {model_error}. Fallback error: {fallback_error}",
                        },
                    )

        # Verify query engine is actually initialized
        if not app.state.query_engine:
            logger.error(
                "Query engine still not available after initialization attempts"
            )
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Unable to initialize query engine",
                    "details": "Please contact the administrator",
                },
            )

        # Process query and get result
        logger.info(f"Processing validated query")

        # Enhance the query to request more detailed responses
        enhanced_query = f"""Please provide a detailed and comprehensive answer to the following question. Include relevant examples, explanations, and context where appropriate. Break down complex concepts into understandable parts.

Question: {validated_query}"""

        # Process query and get result using safe operations
        result = await safe_index_ops.safe_query(app.state.index, app.state.query_engine, enhanced_query)
        logger.info(f"Query result type: {type(result)}")  # Log type for debugging

        # --- Log raw result structure ---
        logger.debug(f"Raw query result object: {result}")
        logger.debug(f"Raw query result source nodes: {result.source_nodes}")
        logger.debug(f"Raw query result metadata: {result.metadata}")
        # --- End log raw result structure ---

        # Assuming result is a Response object from LlamaIndex
        response_content = result.response  # Access the actual response string
        source_nodes = result.source_nodes  # Access source nodes

        # Prepare follow-up questions (Example logic, adjust as needed)
        follow_up_questions = []
        # <<< Restore manual follow-up question generation >>>
        try:
            # Check if LLM is available on app state
            if hasattr(app.state, "llm") and app.state.llm:
                prompt = f"""Based on the answer provided above about '{query}', suggest exactly 3 concise follow-up questions a user might ask to explore the topic further. Output ONLY the questions, each on a new line, without any numbering or introduction.

Answer:
{response_content}"""

                # Use await directly as llm.acomplete should be async
                fu_resp = await app.state.llm.acomplete(prompt)

                # Parse response, removing potential numbering/bullets
                questions_raw = fu_resp.text.strip().split("\n")
                follow_up_questions = [
                    q.strip(" *-1234567890.) ")
                    for q in questions_raw
                    if q.strip()
                    and len(q.strip()) > 5  # Basic check for valid question
                ][:3]  # Take at most 3
                logger.info(f"Generated follow-up questions: {follow_up_questions}")
            else:
                logger.warning(
                    "LLM not available on app state, cannot generate follow-up questions."
                )
        except Exception as fu_err:
            logger.warning(
                f"Follow-up question generation failed: {fu_err}", exc_info=True
            )
        # <<< End restore >>>

        logger.info(
            f"Query response content: {response_content[:200]}..."
        )  # Log snippet

        return JSONResponse(
            status_code=200,
            content={
                "status": "completed",
                "response": response_content,  # Send the actual response string
                "source_nodes": [
                    {
                        # Use node.node to access the underlying TextNode if it's NodeWithScore
                        "text": node.node.text if hasattr(node, "node") else node.text,
                        "snippet": (
                            node.node.text[:200]
                            + ("…" if len(node.node.text) > 200 else "")
                        )
                        if hasattr(node, "node")
                        else (node.text[:200] + ("…" if len(node.text) > 200 else "")),
                        "score": getattr(node, "score", None),
                        "metadata": node.node.metadata
                        if hasattr(node, "node")
                        else node.metadata,
                    }
                    for node in source_nodes
                ],
                "follow_up_questions": follow_up_questions,
            },
        )

    except ConnectionError as e:
        logger.error(f"Network connection error in query endpoint: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "error": "Service temporarily unavailable",
                "details": "Unable to connect to required services. Please try again later.",
            },
        )
    except TimeoutError as e:
        logger.error(f"Timeout error in query endpoint: {str(e)}")
        return JSONResponse(
            status_code=504,
            content={
                "error": "Request timeout",
                "details": "The query took too long to process. Please try a simpler query.",
            },
        )
    except ValueError as e:
        logger.error(f"Value error in query endpoint: {str(e)}")
        return JSONResponse(
            status_code=400,
            content={
                "error": "Invalid query parameters",
                "details": "The query contains invalid parameters or format.",
            },
        )
    except Exception as e:
        error_type = type(e).__name__
        logger.error(f"Unexpected error ({error_type}) in query endpoint: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "An unexpected error occurred while processing the query",
                "details": f"Error type: {error_type}. Please contact support if this persists.",
            },
        )


@app.get("/api/documents")
async def get_indexed_documents(request: Request):
    """Returns metadata for documents currently in the vector index."""
    logger.info(f"[/api/documents] Request received from {request.client.host}")

    # Temporarily bypass authentication to debug the issue
    session = request.session
    logger.info(f"[/api/documents] Session content: {dict(session)}")
    logger.info(f"[/api/documents] Session keys: {list(session.keys())}")

    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for /api/documents")
        raise HTTPException(status_code=503, detail="Index is not ready.")

    try:
        # Access the document store using safe operations
        documents = await safe_index_ops.safe_read_docstore(app.state.index)
        
        docs_metadata = []
        for doc_id, doc_data in documents.items():
            metadata = doc_data['metadata'].copy()
            metadata["doc_id"] = doc_id  # Add the internal doc_id
            docs_metadata.append(metadata)

        logger.info(f"Returning metadata for {len(docs_metadata)} indexed documents.")
        return JSONResponse(content={"documents": docs_metadata})

    except Exception as e:
        logger.error(f"Error retrieving indexed documents: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to retrieve indexed documents metadata."
        )


@app.post("/api/documents/clear")
async def clear_indexed_documents(request: Request, user=CurrentUser, csrf_valid: bool = CSRFProtect, admin_user: str = AdminRequired):
    """Clears all documents from the vector index. Requires admin privileges."""
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for clearing documents")
        raise HTTPException(status_code=503, detail="Index is not ready.")

    try:
        # Log admin action
        action_desc = "clear all documents from vector index"
        log_admin_action(request.session, action_desc)
        
        # Create a new empty index
        app.state.index = await create_empty_index()
        await persist_index()
        
        # Log successful completion
        log_admin_action(request.session, f"successfully completed {action_desc}")
        
        return JSONResponse(content={"message": "All documents cleared successfully"})
    except Exception as e:
        error_msg = str(e)
        log_admin_action(request.session, action_desc, success=False, error=error_msg)
        
        logger.error(f"Error clearing documents: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to clear documents from index."
        )


# CSRF token endpoint
@app.get("/api/csrf-token")
async def get_csrf_token_endpoint(request: Request):
    """Get CSRF token for the current session."""
    try:
        csrf_token = get_csrf_token(request)
        return JSONResponse(content={"csrf_token": csrf_token})
    except Exception as e:
        logger.error(f"Error generating CSRF token: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": "Failed to generate CSRF token"}
        )

# Health check endpoint
@app.api_route("/health", methods=["GET", "HEAD"])
async def health_check():
    """Endpoint to check if the service is alive and ready."""
    return JSONResponse(content={"status": "healthy"})


# Root endpoint to render the index page
@app.get("/")
async def root(request: Request):
    """Render the main index template for the application."""
    # Restore original logic
    is_admin = user_is_admin(request.session)
    logger.info(f"[/] Root page loaded. Admin status: {is_admin}")  # Simple log
    return templates.TemplateResponse(
        "index.html", {"request": request, "is_admin": is_admin}
    )


# --- Add missing /documents route ---#
@app.get("/documents", name="documents_page")
async def documents_page_redirect(request: Request, user: dict = CurrentUser):
    """Redirects to the main page. Exists to satisfy url_for calls."""
    logger.info("Redirecting from /documents to root (/)")
    return RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)


# --- End add route ---


# Note: Admin authorization logic moved to centralized authorization.py module


# New endpoint for manual synchronization
@app.post("/sync/sharepoint/{drive_id}", name="sync_sharepoint_drive")
async def sync_sharepoint_drive(
    request: Request,
    drive_id: str,
    folder_path: str = Query(
        "",
        description="Optional subfolder path within the drive to sync. Default is root.",
    ),
    user=CurrentUser,
    ms_token: str = MsTokenDep,
    csrf_valid: bool = CSRFProtect,
    admin_user: str = AdminRequired,
):
    """Manually syncs the index with the current state of a SharePoint drive/folder. Requires admin privileges."""
    # Log admin action
    folder_desc = f"folder '{folder_path}'" if folder_path else "root folder"
    action_desc = f"sync SharePoint drive {drive_id} ({folder_desc})"
    log_admin_action(request.session, action_desc)
    
    logger.info(
        f"Starting manual sync for Drive ID: {drive_id}, Folder Path: '{folder_path or 'root'}'"
    )
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for sync operation")
        raise HTTPException(status_code=503, detail="Index is not ready.")
    if not hasattr(app.state, "sharepoint_client"):
        logger.error("SharePoint client not available for sync operation")
        raise HTTPException(
            status_code=503, detail="SharePoint client is not configured or ready."
        )

    current_sp_ids = set()
    deleted_count = 0
    errors = []

    try:
        # 1. Get all current item IDs from SharePoint
        logger.info("Fetching current item IDs from SharePoint...")
        # Determine target folder ID (root or specific subfolder)
        target_item_id = "root"  # Default to root
        if folder_path:
            try:
                folder_item = await app.state.sharepoint_client.get_item_by_path(
                    drive_id, folder_path, token=ms_token
                )
                if folder_item and "id" in folder_item:
                    target_item_id = folder_item["id"]
                    logger.info(
                        f"Targeting folder '{folder_path}' with ID: {target_item_id}"
                    )
                else:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Folder path '{folder_path}' not found in drive {drive_id}.",
                    )
            except Exception as e:
                logger.error(
                    f"Error getting folder ID for path '{folder_path}': {e}",
                    exc_info=True,
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to resolve folder path '{folder_path}'.",
                )

        # Recursively list files
        async for item in app.state.sharepoint_client.list_folder_contents_recursive(
            drive_id, target_item_id, token=ms_token
        ):
            if "file" in item:  # We only care about files for the index
                current_sp_ids.add(item.get("id"))

        logger.info(
            f"Found {len(current_sp_ids)} current file items in SharePoint target."
        )

        # 2. Get all sharepoint_ids from the index
        indexed_sp_ids = {}
        if hasattr(app.state.index, "docstore") and hasattr(
            app.state.index.docstore, "docs"
        ):
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                sp_id = doc_info.metadata.get("sharepoint_id")
                if sp_id:
                    indexed_sp_ids[sp_id] = doc_id
        logger.info(
            f"Found {len(indexed_sp_ids)} items with sharepoint_id in the index."
        )

        # 3. Compare and find items to delete
        ids_in_index = set(indexed_sp_ids.keys())
        ids_to_delete = ids_in_index - current_sp_ids
        logger.info(f"Identified {len(ids_to_delete)} items to remove from the index.")

        # 4. Delete items from index
        if ids_to_delete:
            tasks = []
            for sp_id_to_delete in ids_to_delete:
                doc_id_to_delete = indexed_sp_ids[sp_id_to_delete]
                try:
                    # Run potentially blocking delete in thread
                    # Pass delete_from_docstore=True to remove from docstore as well
                    await asyncio.to_thread(
                        app.state.index.delete_ref_doc,
                        doc_id_to_delete,
                        delete_from_docstore=True,
                    )
                    logger.info(
                        f"Deleted doc_id {doc_id_to_delete} (SharePoint ID: {sp_id_to_delete}) from index."
                    )
                    deleted_count += 1
                except Exception as e:
                    error_msg = f"Failed to delete doc_id {doc_id_to_delete} (SharePoint ID: {sp_id_to_delete}): {e}"
                    logger.error(error_msg)
                    errors.append(error_msg)

            # 5. Persist index changes after deletions
            if deleted_count > 0:
                logger.info("Persisting index changes after deletions...")
                await persist_index()
                logger.info("Index persistence complete.")
            else:
                logger.info("No actual deletions performed, skipping persistence.")
        else:
            logger.info("No items to delete from the index.")

        return JSONResponse(
            content={
                "status": "sync_completed",
                "drive_id": drive_id,
                "folder_path": folder_path or "root",
                "sharepoint_items_found": len(current_sp_ids),
                "indexed_items_checked": len(indexed_sp_ids),
                "items_deleted_from_index": deleted_count,
                "errors": errors,
            }
        )

    except HTTPException as he:
        raise he  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error during SharePoint sync: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Sync failed: {str(e)}")
