"""
Comprehensive input validation module for the DDB SharePoint RAG application.
Provides secure validation for file paths, SharePoint IDs, and other user inputs.
"""

import re
import logging
from pathlib import Path, PurePosixPath
from typing import Union, Optional, List
from urllib.parse import unquote, quote
import uuid

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom exception for input validation errors."""
    pass

class InputValidator:
    """Comprehensive input validator for file paths, SharePoint IDs, and other inputs."""
    
    # SharePoint ID patterns
    SHAREPOINT_ID_PATTERN = re.compile(r'^[a-zA-Z0-9\-_!]+$')
    SHAREPOINT_SITE_ID_PATTERN = re.compile(r'^[a-zA-Z0-9\-._,!]+$')
    
    # File extension allowlist
    ALLOWED_EXTENSIONS = {
        '.txt', '.pdf', '.docx', '.pptx', '.xlsx', '.csv', '.md',
        '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.rtf', '.doc', '.xls'
    }
    
    # Maximum lengths
    MAX_FILENAME_LENGTH = 255
    MAX_PATH_LENGTH = 4096
    MAX_SHAREPOINT_ID_LENGTH = 100
    MAX_QUERY_LENGTH = 500
    
    # Dangerous path components
    DANGEROUS_PATH_COMPONENTS = {
        '..', '.', '~', '$', '*', '?', '<', '>', '|', ':', '"',
        'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4',
        'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2',
        'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    }
    
    @classmethod
    def validate_filename(cls, filename: str) -> str:
        """
        Validate and sanitize a filename.
        
        Args:
            filename: The filename to validate
            
        Returns:
            str: Sanitized filename
            
        Raises:
            ValidationError: If filename is invalid
        """
        if not filename or not isinstance(filename, str):
            raise ValidationError("Filename must be a non-empty string")
        
        # Remove any path separators
        filename = Path(filename).name
        
        # Check length
        if len(filename) > cls.MAX_FILENAME_LENGTH:
            raise ValidationError(f"Filename too long (max {cls.MAX_FILENAME_LENGTH} characters)")
        
        # Check for dangerous characters
        if any(char in filename for char in '<>:"|?*\x00'):
            raise ValidationError("Filename contains invalid characters")
        
        # Check for dangerous names
        name_without_ext = Path(filename).stem.upper()
        if name_without_ext in cls.DANGEROUS_PATH_COMPONENTS:
            raise ValidationError("Filename is a reserved system name")
        
        # Check file extension
        extension = Path(filename).suffix.lower()
        if extension and extension not in cls.ALLOWED_EXTENSIONS:
            raise ValidationError(f"File extension '{extension}' is not allowed")
        
        # Remove leading/trailing whitespace and dots
        filename = filename.strip(' .')
        
        if not filename:
            raise ValidationError("Filename is empty after sanitization")
        
        logger.debug(f"Validated filename: {filename}")
        return filename
    
    @classmethod
    def validate_file_path(cls, file_path: Union[str, Path], base_dir: Optional[Path] = None) -> Path:
        """
        Validate and sanitize a file path to prevent directory traversal.
        
        Args:
            file_path: The file path to validate
            base_dir: Optional base directory to constrain the path within
            
        Returns:
            Path: Sanitized and validated path
            
        Raises:
            ValidationError: If path is invalid or unsafe
        """
        if not file_path:
            raise ValidationError("File path cannot be empty")
        
        try:
            # Convert to Path object
            path = Path(file_path)
            
            # Check total length
            if len(str(path)) > cls.MAX_PATH_LENGTH:
                raise ValidationError(f"Path too long (max {cls.MAX_PATH_LENGTH} characters)")
            
            # Resolve path to handle .. and . components
            resolved_path = path.resolve()
            
            # Check for dangerous components
            for part in path.parts:
                if part in cls.DANGEROUS_PATH_COMPONENTS:
                    raise ValidationError(f"Path contains dangerous component: {part}")
            
            # If base_dir is specified, ensure path is within it
            if base_dir:
                base_dir = Path(base_dir).resolve()
                try:
                    resolved_path.relative_to(base_dir)
                except ValueError:
                    raise ValidationError("Path is outside allowed base directory")
            
            # Validate filename if it's a file
            if path.name:
                cls.validate_filename(path.name)
            
            logger.debug(f"Validated file path: {resolved_path}")
            return resolved_path
            
        except (OSError, ValueError) as e:
            raise ValidationError(f"Invalid file path: {e}")
    
    @classmethod
    def validate_sharepoint_id(cls, sharepoint_id: str) -> str:
        """
        Validate a SharePoint item ID.
        
        Args:
            sharepoint_id: The SharePoint ID to validate
            
        Returns:
            str: Validated SharePoint ID
            
        Raises:
            ValidationError: If ID is invalid
        """
        if not sharepoint_id or not isinstance(sharepoint_id, str):
            raise ValidationError("SharePoint ID must be a non-empty string")
        
        # Remove any URL encoding
        decoded_id = unquote(sharepoint_id)
        
        # Check length
        if len(decoded_id) > cls.MAX_SHAREPOINT_ID_LENGTH:
            raise ValidationError(f"SharePoint ID too long (max {cls.MAX_SHAREPOINT_ID_LENGTH} characters)")
        
        # Check pattern for regular SharePoint IDs
        if not cls.SHAREPOINT_ID_PATTERN.match(decoded_id):
            raise ValidationError("SharePoint ID contains invalid characters")
        
        logger.debug(f"Validated SharePoint ID: {decoded_id}")
        return decoded_id
    
    @classmethod
    def validate_sharepoint_site_id(cls, site_id: str) -> str:
        """
        Validate a SharePoint site ID (can include commas for compound IDs).
        
        Args:
            site_id: The SharePoint site ID to validate
            
        Returns:
            str: Validated SharePoint site ID
            
        Raises:
            ValidationError: If ID is invalid
        """
        if not site_id or not isinstance(site_id, str):
            raise ValidationError("SharePoint site ID must be a non-empty string")
        
        # Remove any URL encoding
        decoded_id = unquote(site_id)
        
        # Check length
        if len(decoded_id) > cls.MAX_SHAREPOINT_ID_LENGTH * 3:  # Allow for compound IDs
            raise ValidationError("SharePoint site ID too long")
        
        # Check pattern for site IDs (allows commas for compound IDs)
        if not cls.SHAREPOINT_SITE_ID_PATTERN.match(decoded_id):
            raise ValidationError("SharePoint site ID contains invalid characters")
        
        logger.debug(f"Validated SharePoint site ID: {decoded_id}")
        return decoded_id
    
    @classmethod
    def validate_folder_path(cls, folder_path: str) -> str:
        """
        Validate a SharePoint folder path.
        
        Args:
            folder_path: The folder path to validate
            
        Returns:
            str: Validated folder path
            
        Raises:
            ValidationError: If path is invalid
        """
        if not folder_path:
            return ""  # Empty folder path is valid (root)
        
        if not isinstance(folder_path, str):
            raise ValidationError("Folder path must be a string")
        
        # Remove URL encoding
        decoded_path = unquote(folder_path)
        
        # Check length
        if len(decoded_path) > cls.MAX_PATH_LENGTH:
            raise ValidationError(f"Folder path too long (max {cls.MAX_PATH_LENGTH} characters)")
        
        # Use PurePosixPath for SharePoint paths (always forward slashes)
        try:
            path = PurePosixPath(decoded_path)
            
            # Check for dangerous components
            for part in path.parts:
                if part in cls.DANGEROUS_PATH_COMPONENTS or part.startswith('.'):
                    raise ValidationError(f"Folder path contains dangerous component: {part}")
                
                # Additional checks for SharePoint folder names
                if any(char in part for char in '<>:"|?*\x00'):
                    raise ValidationError(f"Folder name contains invalid characters: {part}")
            
            # Normalize the path (remove redundant separators, etc.)
            normalized_path = str(path)
            
            logger.debug(f"Validated folder path: {normalized_path}")
            return normalized_path
            
        except ValueError as e:
            raise ValidationError(f"Invalid folder path: {e}")
    
    @classmethod
    def validate_query_string(cls, query: str) -> str:
        """
        Validate a user query string for the RAG system.
        
        Args:
            query: The query string to validate
            
        Returns:
            str: Validated query string
            
        Raises:
            ValidationError: If query is invalid
        """
        if not query or not isinstance(query, str):
            raise ValidationError("Query must be a non-empty string")
        
        # Remove leading/trailing whitespace
        query = query.strip()
        
        # Check length
        if len(query) > cls.MAX_QUERY_LENGTH:
            raise ValidationError(f"Query too long (max {cls.MAX_QUERY_LENGTH} characters)")
        
        # Check for potentially malicious patterns
        suspicious_patterns = [
            r'<script[^>]*>',
            r'javascript:',
            r'data:',
            r'vbscript:',
            r'onload\s*=',
            r'onerror\s*=',
            r'eval\s*\(',
            r'expression\s*\(',
        ]
        
        for pattern in suspicious_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                raise ValidationError("Query contains potentially malicious content")
        
        # Remove any null bytes
        query = query.replace('\x00', '')
        
        if not query:
            raise ValidationError("Query is empty after sanitization")
        
        logger.debug(f"Validated query string: {query[:50]}...")
        return query
    
    @classmethod
    def validate_uuid(cls, uuid_string: str) -> str:
        """
        Validate a UUID string.
        
        Args:
            uuid_string: The UUID to validate
            
        Returns:
            str: Validated UUID string
            
        Raises:
            ValidationError: If UUID is invalid
        """
        if not uuid_string or not isinstance(uuid_string, str):
            raise ValidationError("UUID must be a non-empty string")
        
        try:
            # This will raise ValueError if invalid
            uuid_obj = uuid.UUID(uuid_string)
            validated_uuid = str(uuid_obj)
            logger.debug(f"Validated UUID: {validated_uuid}")
            return validated_uuid
        except ValueError:
            raise ValidationError("Invalid UUID format")
    
    @classmethod
    def sanitize_for_logging(cls, data: str, max_length: int = 100) -> str:
        """
        Sanitize data for safe logging (remove sensitive info, limit length).
        
        Args:
            data: The data to sanitize
            max_length: Maximum length for logging
            
        Returns:
            str: Sanitized data safe for logging
        """
        if not data:
            return ""
        
        # Convert to string if not already
        data_str = str(data)
        
        # Remove potential sensitive patterns
        sensitive_patterns = [
            (r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', '[EMAIL]'),
            (r'([A-Za-z0-9+/]{40,})', '[TOKEN]'),
            (r'(password["\s]*[:=]["\s]*)[^,}]+', r'\1[REDACTED]'),
            (r'(secret["\s]*[:=]["\s]*)[^,}]+', r'\1[REDACTED]'),
            (r'(key["\s]*[:=]["\s]*)[^,}]+', r'\1[REDACTED]'),
        ]
        
        sanitized = data_str
        for pattern, replacement in sensitive_patterns:
            sanitized = re.sub(pattern, replacement, sanitized, flags=re.IGNORECASE)
        
        # Truncate if too long
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length] + "..."
        
        return sanitized

# Global validator instance
validator = InputValidator()