#!/usr/bin/env python3

def fix_indentation():
    with open('app.py', 'r') as f:
        lines = f.readlines()

    # Find the problematic function
    function_start = None
    try_start = None
    except_start = None

    for i, line in enumerate(lines):
        if 'async def import_sharepoint_item(request: Request, drive_id: str, item_id: str) -> dict:' in line:
            function_start = i
            print(f"Found function at line {i+1}")
        elif function_start is not None and '        try:' in line:
            try_start = i
            print(f"Found try block at line {i+1}")
        elif try_start is not None and line.strip().startswith('except Exception as e:') and len(line) - len(line.lstrip()) == 8:
            except_start = i
            print(f"Found except block at line {i+1}")
            break

    if function_start is None or try_start is None or except_start is None:
        print(f"Could not find the required blocks: func={function_start}, try={try_start}, except={except_start}")
        return

    # Fix indentation for lines between try and except
    fixed_count = 0
    for i in range(try_start + 1, except_start):
        line = lines[i]
        if line.strip():  # Only modify non-empty lines
            # Check current indentation
            current_indent = len(line) - len(line.lstrip())
            # Lines inside the try block should have at least 12 spaces
            if current_indent < 12:
                # Add 4 spaces to bring it to the right level
                lines[i] = '    ' + line
                fixed_count += 1
            elif current_indent == 12 and not line.strip().startswith(('def ', 'class ', 'async def ')):
                # This might be a line that should be indented further
                # Check if the previous line ends with ':' and this line should be indented more
                if i > 0 and lines[i-1].strip().endswith(':'):
                    lines[i] = '    ' + line
                    fixed_count += 1

    # Write back to file
    with open('app.py', 'w') as f:
        f.writelines(lines)

    print(f"Fixed indentation for {fixed_count} lines")

if __name__ == "__main__":
    fix_indentation()
