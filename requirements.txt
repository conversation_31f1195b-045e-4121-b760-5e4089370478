fastapi==0.109.2
uvicorn==0.27.1
python-multipart==0.0.9
python-dotenv==1.0.1
llama-index>=0.10.35
llama-index-core>=0.10.35
llama-index-embeddings-openai>=0.3.0
llama-index-llms-openai>=0.3.0
llama-index-postprocessor-cohere-rerank>=0.3.0
llama-index-readers-file>=0.4.7
docx2txt==0.8
jinja2==3.1.3
itsdangerous==2.1.2
pydantic>=2.6.1,<3.0.0
pydantic-settings>=2.1.0,<3.0.0
typing-extensions>=4.9.0
google-cloud-storage>=2.15.0
google-auth>=2.28.0
cryptography>=42.0.0
msal-extensions==1.3.0
msgraph-core==0.2.2
requests>=2.31.0
aiohttp>=3.9.3
Pillow>=10.0.0
pytesseract>=0.3.10
PyPDF2==3.0.1
pandas>=2.0.0
apscheduler==3.10.4

# Test dependencies (optional - install with pip install -r requirements-test.txt)
# pytest>=7.0.0
# pytest-asyncio>=0.21.0
# pytest-mock>=3.10.0
# pytest-cov>=4.0.0
# httpx>=0.24.0