#!/usr/bin/env python3
"""
Test runner script for DDB SharePoint RAG Application.
"""

import sys
import subprocess
import os
from pathlib import Path


def install_test_dependencies():
    """Install test dependencies if not already installed."""
    test_deps = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-mock>=3.10.0",
        "pytest-cov>=4.0.0",
        "httpx>=0.24.0"  # For testing FastAPI
    ]
    
    print("Installing test dependencies...")
    for dep in test_deps:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                         check=True, capture_output=True)
            print(f"✓ Installed {dep}")
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to install {dep}: {e}")
            return False
    
    return True


def run_tests(test_path=None, coverage=False, verbose=False):
    """Run the test suite."""
    cmd = [sys.executable, "-m", "pytest"]
    
    if test_path:
        cmd.append(test_path)
    else:
        cmd.append("tests/")
    
    if verbose:
        cmd.extend(["-v", "-s"])
    
    if coverage:
        cmd.extend([
            "--cov=.",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-exclude=tests/*",
            "--cov-exclude=venv/*",
            "--cov-exclude=__pycache__/*"
        ])
    
    # Add other useful options
    cmd.extend([
        "--tb=short",
        "--color=yes",
        "--durations=10"
    ])
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, cwd=Path(__file__).parent)
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return False
    except Exception as e:
        print(f"Error running tests: {e}")
        return False


def main():
    """Main test runner function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run tests for DDB SharePoint RAG Application")
    parser.add_argument("--install-deps", action="store_true", 
                       help="Install test dependencies")
    parser.add_argument("--coverage", action="store_true",
                       help="Run tests with coverage reporting")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    parser.add_argument("--test-path", type=str,
                       help="Specific test file or directory to run")
    parser.add_argument("--unit", action="store_true",
                       help="Run only unit tests")
    parser.add_argument("--integration", action="store_true", 
                       help="Run only integration tests")
    
    args = parser.parse_args()
    
    # Change to script directory
    os.chdir(Path(__file__).parent)
    
    if args.install_deps:
        if not install_test_dependencies():
            print("Failed to install dependencies")
            return 1
    
    # Determine test path based on arguments
    test_path = args.test_path
    if args.unit:
        test_path = "tests/ -m unit"
    elif args.integration:
        test_path = "tests/ -m integration"
    
    print("=" * 60)
    print("DDB SharePoint RAG Application - Test Suite")
    print("=" * 60)
    
    success = run_tests(test_path, args.coverage, args.verbose)
    
    if success:
        print("\n✓ All tests passed!")
        if args.coverage:
            print("Coverage report generated in htmlcov/index.html")
        return 0
    else:
        print("\n✗ Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
