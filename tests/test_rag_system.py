"""
Tests for RAG (Retrieval-Augmented Generation) system functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock

# Import RAG components to test
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import get_query_engine
from index_locking import IndexLockManager, SafeIndexOperations


class TestRAGSystem:
    """Test RAG system functionality."""
    
    @pytest.fixture
    def mock_index(self):
        """Create a mock LlamaIndex for testing."""
        index = Mock()
        index.as_retriever = Mock()
        index.storage_context = Mock()
        index.storage_context.persist = Mock()
        index.insert_nodes = Mock()
        index.docstore = Mock()
        index.docstore.docs = {}
        return index
    
    @pytest.fixture
    def mock_reranker(self):
        """Create a mock reranker for testing."""
        reranker = Mock()
        return reranker
    
    def test_get_query_engine_creation(self, mock_index, mock_reranker):
        """Test query engine creation."""
        with patch('app.VectorIndexRetriever') as mock_retriever_class, \
             patch('app.RetrieverQueryEngine') as mock_engine_class, \
             patch('app.get_response_synthesizer') as mock_synthesizer:
            
            mock_retriever = Mock()
            mock_retriever_class.return_value = mock_retriever
            
            mock_engine = Mock()
            mock_engine_class.from_args.return_value = mock_engine
            
            mock_synth = Mock()
            mock_synthesizer.return_value = mock_synth
            
            result = get_query_engine(mock_index, mock_reranker)
            
            assert result == mock_engine
            mock_retriever_class.assert_called_once()
            mock_engine_class.from_args.assert_called_once()
    
    def test_get_query_engine_without_reranker(self, mock_index):
        """Test query engine creation without reranker."""
        with patch('app.VectorIndexRetriever') as mock_retriever_class, \
             patch('app.RetrieverQueryEngine') as mock_engine_class, \
             patch('app.get_response_synthesizer') as mock_synthesizer:
            
            mock_retriever = Mock()
            mock_retriever_class.return_value = mock_retriever
            
            mock_engine = Mock()
            mock_engine_class.from_args.return_value = mock_engine
            
            mock_synth = Mock()
            mock_synthesizer.return_value = mock_synth
            
            result = get_query_engine(mock_index, None)
            
            assert result == mock_engine
            # Should still create engine even without reranker
            mock_engine_class.from_args.assert_called_once()


class TestIndexLocking:
    """Test index locking functionality."""
    
    @pytest.fixture
    def lock_manager(self):
        """Create an index lock manager for testing."""
        return IndexLockManager()
    
    @pytest.fixture
    def safe_ops(self, lock_manager):
        """Create safe index operations for testing."""
        return SafeIndexOperations(lock_manager)
    
    @pytest.mark.asyncio
    async def test_read_lock_acquisition(self, lock_manager):
        """Test read lock acquisition and release."""
        async with lock_manager.read_lock("test_operation"):
            # Should be able to acquire read lock
            assert lock_manager._read_count > 0
        
        # Lock should be released after context
        assert lock_manager._read_count == 0
    
    @pytest.mark.asyncio
    async def test_write_lock_acquisition(self, lock_manager):
        """Test write lock acquisition and release."""
        async with lock_manager.write_lock("test_operation"):
            # Should be able to acquire write lock
            assert lock_manager._write_lock.locked()
        
        # Lock should be released after context
        assert not lock_manager._write_lock.locked()
    
    @pytest.mark.asyncio
    async def test_multiple_read_locks(self, lock_manager):
        """Test that multiple read locks can be acquired simultaneously."""
        async def read_operation(op_name):
            async with lock_manager.read_lock(op_name):
                await asyncio.sleep(0.1)  # Simulate work
                return f"completed_{op_name}"
        
        # Start multiple read operations concurrently
        tasks = [
            read_operation("read1"),
            read_operation("read2"),
            read_operation("read3")
        ]
        
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 3
        assert all("completed_" in result for result in results)
    
    @pytest.mark.asyncio
    async def test_write_lock_exclusivity(self, lock_manager):
        """Test that write lock is exclusive."""
        results = []
        
        async def write_operation(op_name):
            async with lock_manager.write_lock(op_name):
                results.append(f"start_{op_name}")
                await asyncio.sleep(0.1)  # Simulate work
                results.append(f"end_{op_name}")
        
        # Start two write operations concurrently
        await asyncio.gather(
            write_operation("write1"),
            write_operation("write2")
        )
        
        # Operations should be sequential, not interleaved
        assert results == ["start_write1", "end_write1", "start_write2", "end_write2"] or \
               results == ["start_write2", "end_write2", "start_write1", "end_write1"]
    
    @pytest.mark.asyncio
    async def test_safe_insert_nodes(self, safe_ops, mock_index):
        """Test safe node insertion."""
        mock_nodes = [Mock(), Mock()]
        
        await safe_ops.safe_insert_nodes(mock_index, mock_nodes)
        
        mock_index.insert_nodes.assert_called_once_with(mock_nodes)
    
    @pytest.mark.asyncio
    async def test_safe_persist_index(self, safe_ops, mock_index):
        """Test safe index persistence."""
        persist_dir = "/test/persist/dir"
        
        await safe_ops.safe_persist_index(mock_index, persist_dir)
        
        mock_index.storage_context.persist.assert_called_once_with(persist_dir=persist_dir)
    
    @pytest.mark.asyncio
    async def test_safe_query(self, safe_ops, mock_index):
        """Test safe query execution."""
        mock_query_engine = Mock()
        mock_response = Mock()
        mock_response.response = "Test response"
        mock_response.source_nodes = []
        mock_response.metadata = {}
        
        mock_query_engine.query.return_value = mock_response
        
        result = await safe_ops.safe_query(mock_index, mock_query_engine, "test query")
        
        assert result == mock_response
        mock_query_engine.query.assert_called_once_with("test query")
    
    @pytest.mark.asyncio
    async def test_safe_read_docstore(self, safe_ops, mock_index):
        """Test safe docstore reading."""
        # Setup mock docstore
        mock_doc1 = Mock()
        mock_doc1.metadata = {"file_name": "doc1.txt", "type": "text"}
        mock_doc2 = Mock()
        mock_doc2.metadata = {"file_name": "doc2.pdf", "type": "pdf"}
        
        mock_index.docstore.docs = {
            "doc1": mock_doc1,
            "doc2": mock_doc2
        }
        
        result = await safe_ops.safe_read_docstore(mock_index)
        
        assert len(result) == 2
        assert "doc1" in result
        assert "doc2" in result
        assert result["doc1"]["metadata"]["file_name"] == "doc1.txt"
        assert result["doc2"]["metadata"]["file_name"] == "doc2.pdf"
    
    def test_lock_statistics(self, lock_manager):
        """Test lock statistics tracking."""
        stats = lock_manager.get_stats()
        
        assert hasattr(stats, 'read_operations')
        assert hasattr(stats, 'write_operations')
        assert hasattr(stats, 'lock_wait_time')
        assert hasattr(stats, 'last_operation_time')
        
        # Initial stats should be zero
        assert stats.read_operations == 0
        assert stats.write_operations == 0
        assert stats.lock_wait_time == 0.0
    
    @pytest.mark.asyncio
    async def test_operation_history_tracking(self, lock_manager):
        """Test that operation history is tracked."""
        async with lock_manager.read_lock("test_read"):
            pass
        
        async with lock_manager.write_lock("test_write"):
            pass
        
        # Check that operations were recorded
        assert len(lock_manager._operation_history) == 2
        
        read_op = lock_manager._operation_history[0]
        write_op = lock_manager._operation_history[1]
        
        assert read_op['type'] == 'read'
        assert read_op['operation'] == 'test_read'
        assert write_op['type'] == 'write'
        assert write_op['operation'] == 'test_write'
