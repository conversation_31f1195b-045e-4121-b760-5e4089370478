"""
Pytest configuration and fixtures for DDB SharePoint RAG Application tests.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, AsyncMock
import asyncio
from fastapi.testclient import TestClient

# Import the main app and components
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from config import Settings
from sharepoint_client import SharePointClient


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path)


@pytest.fixture
def mock_settings(temp_dir):
    """Create mock settings for testing."""
    settings = Settings(
        STORAGE_DIR=temp_dir / "storage",
        DATA_DIR=temp_dir / "data",
        UPLOAD_DIR=temp_dir / "uploads",
        TEMP_DIR=temp_dir / "temp",
        OPENAI_API_KEY="test-key",
        COHERE_API_KEY="test-key",
        SESSION_SECRET_KEY="test-secret",
        ADMIN_USERNAME="test-admin",
        ADMIN_PASSWORD="test-password",
        USE_SHAREPOINT=False
    )
    
    # Create directories
    settings.STORAGE_DIR.mkdir(parents=True, exist_ok=True)
    settings.DATA_DIR.mkdir(parents=True, exist_ok=True)
    settings.UPLOAD_DIR.mkdir(parents=True, exist_ok=True)
    settings.TEMP_DIR.mkdir(parents=True, exist_ok=True)
    
    return settings


@pytest.fixture
def mock_sharepoint_client():
    """Create a mock SharePoint client for testing."""
    client = Mock(spec=SharePointClient)
    client.get_sites = AsyncMock(return_value=[
        {"id": "site1", "name": "Test Site 1", "webUrl": "https://test.sharepoint.com/site1"},
        {"id": "site2", "name": "Test Site 2", "webUrl": "https://test.sharepoint.com/site2"}
    ])
    client.get_drives = AsyncMock(return_value=[
        {"id": "drive1", "name": "Documents", "driveType": "documentLibrary"}
    ])
    client.list_files = AsyncMock(return_value=[
        {
            "id": "file1",
            "name": "test.pdf",
            "size": 1024,
            "created_datetime": "2024-01-01T00:00:00Z",
            "last_modified_datetime": "2024-01-01T00:00:00Z",
            "is_folder": False
        }
    ])
    client.download_file = AsyncMock()
    return client


@pytest.fixture
def sample_pdf_file(temp_dir):
    """Create a sample PDF file for testing."""
    pdf_path = temp_dir / "test.pdf"
    # Create a minimal PDF content
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Hello World) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
    
    with open(pdf_path, 'wb') as f:
        f.write(pdf_content)
    
    return pdf_path


@pytest.fixture
def sample_text_file(temp_dir):
    """Create a sample text file for testing."""
    text_path = temp_dir / "test.txt"
    with open(text_path, 'w') as f:
        f.write("This is a test document for the RAG system.\nIt contains sample content for testing.")
    return text_path


@pytest.fixture
def mock_llama_index():
    """Mock LlamaIndex components."""
    mock_index = Mock()
    mock_index.insert_nodes = Mock()
    mock_index.storage_context = Mock()
    mock_index.storage_context.persist = Mock()
    
    mock_query_engine = Mock()
    mock_query_engine.query = Mock()
    
    return {
        "index": mock_index,
        "query_engine": mock_query_engine
    }


@pytest.fixture
def authenticated_headers():
    """Headers for authenticated requests."""
    return {
        "Authorization": "Bearer test-token",
        "Content-Type": "application/json"
    }
