"""
Tests for API endpoints.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient

# Import the app to test
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app


class TestAPIEndpoints:
    """Test API endpoint functionality."""
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)
    
    def test_config_js_endpoint(self, client):
        """Test the config.js endpoint."""
        response = client.get("/config.js")
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/javascript; charset=utf-8"
        assert "window.__RAG_BASE" in response.text
    
    def test_health_check_endpoint(self, client):
        """Test health check endpoint if it exists."""
        # This test assumes a health endpoint exists or will be created
        response = client.get("/health")
        
        # If endpoint doesn't exist, it should return 404
        # If it exists, it should return 200
        assert response.status_code in [200, 404]
    
    def test_root_endpoint_redirect(self, client):
        """Test that root endpoint redirects appropriately."""
        response = client.get("/", allow_redirects=False)
        
        # Should either return content or redirect
        assert response.status_code in [200, 301, 302, 307, 308]
    
    @patch('app.validator')
    def test_query_endpoint_validation_error(self, mock_validator, client):
        """Test query endpoint with validation error."""
        from input_validation import ValidationError
        
        mock_validator.validate_query_string.side_effect = ValidationError("Invalid query")
        mock_validator.sanitize_for_logging.return_value = "sanitized query"
        
        # Mock authentication
        with patch('app.CurrentUser', return_value="test_user"):
            response = client.get("/query?query=invalid_query")
            
            assert response.status_code == 400
            assert "Invalid query format" in response.json()["error"]
    
    @patch('app.app.state')
    def test_query_endpoint_no_index(self, mock_state, client):
        """Test query endpoint when index is not available."""
        mock_state.index = None
        mock_state.query_engine = None
        
        with patch('app.CurrentUser', return_value="test_user"), \
             patch('app.validator') as mock_validator, \
             patch('app.load_index') as mock_load_index:
            
            mock_validator.validate_query_string.return_value = "test query"
            mock_validator.sanitize_for_logging.return_value = "test query"
            mock_load_index.side_effect = Exception("Failed to load index")
            
            response = client.get("/query?query=test")
            
            assert response.status_code == 500
            assert "Failed to load vector index" in response.json()["error"]
    
    def test_static_files_mounting(self, client):
        """Test that static files are properly mounted."""
        # Test accessing a static file (if it exists)
        response = client.get("/static/ddb-logo-80x80.png")
        
        # Should either return the file or 404 if not found
        assert response.status_code in [200, 404]
    
    def test_cors_headers(self, client):
        """Test CORS headers are properly set."""
        response = client.options("/query")
        
        # Should have CORS headers
        assert "access-control-allow-origin" in response.headers or response.status_code == 405
    
    @patch('app.app.state')
    def test_documents_endpoint_access(self, mock_state, client):
        """Test documents endpoint access."""
        with patch('app.CurrentUser', return_value="test_user"):
            response = client.get("/documents")
            
            # Should either return content or require authentication
            assert response.status_code in [200, 401, 403]
    
    def test_error_handling_404(self, client):
        """Test 404 error handling."""
        response = client.get("/nonexistent-endpoint")
        
        assert response.status_code == 404
    
    def test_method_not_allowed(self, client):
        """Test method not allowed handling."""
        # Try POST on a GET-only endpoint
        response = client.post("/config.js")
        
        assert response.status_code == 405
    
    @patch('app.app.state')
    def test_sharepoint_sites_endpoint(self, mock_state, client):
        """Test SharePoint sites endpoint."""
        mock_sharepoint_client = Mock()
        mock_sharepoint_client.get_sites = AsyncMock(return_value=[
            {"id": "site1", "name": "Test Site", "webUrl": "https://test.com"}
        ])
        mock_state.sharepoint_client = mock_sharepoint_client
        
        with patch('app.CurrentUser', return_value="test_user"), \
             patch('app.get_microsoft_access_token', return_value="test_token"):
            
            response = client.get("/sharepoint/sites")
            
            # Should either return sites or require authentication
            assert response.status_code in [200, 401, 403]
    
    def test_request_size_limits(self, client):
        """Test request size limits."""
        # Test with a very large query parameter
        large_query = "x" * 10000  # 10KB query
        
        response = client.get(f"/query?query={large_query}")
        
        # Should either handle gracefully or return appropriate error
        assert response.status_code in [200, 400, 413, 414]
    
    def test_concurrent_requests(self, client):
        """Test handling of concurrent requests."""
        import threading
        import time
        
        results = []
        
        def make_request():
            try:
                response = client.get("/config.js")
                results.append(response.status_code)
            except Exception as e:
                results.append(str(e))
        
        # Create multiple threads to make concurrent requests
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert len(results) == 5
        assert all(result == 200 for result in results)
    
    def test_security_headers(self, client):
        """Test that appropriate security headers are set."""
        response = client.get("/config.js")
        
        # Check for basic security headers
        headers = response.headers
        
        # These headers might be set by middleware
        security_headers = [
            "x-content-type-options",
            "x-frame-options", 
            "x-xss-protection",
            "strict-transport-security"
        ]
        
        # At least some security measures should be in place
        # This is more of a reminder to implement security headers
        assert response.status_code == 200
