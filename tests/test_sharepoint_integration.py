"""
Tests for SharePoint integration functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import aiohttp

# Import the SharePoint client to test
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sharepoint_client import SharePointClient


class TestSharePointClient:
    """Test SharePoint client functionality."""
    
    @pytest.fixture
    def sharepoint_client(self):
        """Create a SharePoint client instance for testing."""
        return SharePointClient(
            tenant_id="test-tenant",
            client_id="test-client",
            client_secret="test-secret",
            site_name="test-site",
            library_name="Documents"
        )
    
    @pytest.mark.asyncio
    async def test_get_sites_success(self, sharepoint_client):
        """Test successful retrieval of SharePoint sites."""
        mock_response_data = {
            "value": [
                {
                    "id": "site1",
                    "name": "Test Site 1",
                    "webUrl": "https://test.sharepoint.com/sites/site1"
                },
                {
                    "id": "site2", 
                    "name": "Test Site 2",
                    "webUrl": "https://test.sharepoint.com/sites/site2"
                }
            ]
        }
        
        with patch.object(sharepoint_client, '_make_graph_request') as mock_request:
            mock_response = Mock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_response_data)
            mock_request.return_value = mock_response
            
            sites = await sharepoint_client.get_sites("test-token")
            
            assert len(sites) == 2
            assert sites[0]["name"] == "Test Site 1"
            assert sites[1]["name"] == "Test Site 2"
            mock_request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_sites_error_handling(self, sharepoint_client):
        """Test error handling in get_sites."""
        with patch.object(sharepoint_client, '_make_graph_request') as mock_request:
            mock_response = Mock()
            mock_response.status = 401
            mock_response.text = AsyncMock(return_value="Unauthorized")
            mock_request.return_value = mock_response
            
            with pytest.raises(Exception) as exc_info:
                await sharepoint_client.get_sites("invalid-token")
            
            assert "401" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_list_files_success(self, sharepoint_client):
        """Test successful file listing."""
        mock_response_data = {
            "value": [
                {
                    "id": "file1",
                    "name": "document.pdf",
                    "size": 1024,
                    "createdDateTime": "2024-01-01T00:00:00Z",
                    "lastModifiedDateTime": "2024-01-01T00:00:00Z",
                    "webUrl": "https://test.sharepoint.com/file1"
                },
                {
                    "id": "folder1",
                    "name": "Subfolder",
                    "folder": {},
                    "webUrl": "https://test.sharepoint.com/folder1"
                }
            ]
        }
        
        with patch.object(sharepoint_client, '_make_graph_request') as mock_request:
            mock_response = Mock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_response_data)
            mock_request.return_value = mock_response
            
            files = await sharepoint_client.list_files("drive1", "test-token")
            
            assert len(files) == 2
            assert files[0]["name"] == "document.pdf"
            assert files[0]["is_folder"] == False
            assert files[1]["name"] == "Subfolder"
            assert files[1]["is_folder"] == True
    
    @pytest.mark.asyncio
    async def test_download_file_success(self, sharepoint_client, temp_dir):
        """Test successful file download."""
        test_content = b"Test file content"
        download_path = temp_dir / "downloaded_file.txt"
        
        with patch.object(sharepoint_client, '_make_graph_request') as mock_request:
            # Mock the download URL request
            mock_response1 = Mock()
            mock_response1.status = 200
            mock_response1.json = AsyncMock(return_value={
                "@microsoft.graph.downloadUrl": "https://download.url/file"
            })
            
            # Mock the actual download request
            mock_response2 = Mock()
            mock_response2.status = 200
            mock_response2.read = AsyncMock(return_value=test_content)
            
            mock_request.side_effect = [mock_response1, mock_response2]
            
            await sharepoint_client.download_file(
                "drive1", "file1", str(download_path), "test-token"
            )
            
            # Verify file was created with correct content
            assert download_path.exists()
            assert download_path.read_bytes() == test_content
    
    @pytest.mark.asyncio
    async def test_download_file_not_found(self, sharepoint_client, temp_dir):
        """Test download file error handling for file not found."""
        download_path = temp_dir / "nonexistent_file.txt"
        
        with patch.object(sharepoint_client, '_make_graph_request') as mock_request:
            mock_response = Mock()
            mock_response.status = 404
            mock_response.text = AsyncMock(return_value="Not Found")
            mock_request.return_value = mock_response
            
            with pytest.raises(Exception) as exc_info:
                await sharepoint_client.download_file(
                    "drive1", "nonexistent", str(download_path), "test-token"
                )
            
            assert "404" in str(exc_info.value)
            assert not download_path.exists()
    
    @pytest.mark.asyncio
    async def test_get_drives_success(self, sharepoint_client):
        """Test successful retrieval of drives."""
        mock_response_data = {
            "value": [
                {
                    "id": "drive1",
                    "name": "Documents",
                    "driveType": "documentLibrary"
                },
                {
                    "id": "drive2",
                    "name": "Shared Documents", 
                    "driveType": "documentLibrary"
                }
            ]
        }
        
        with patch.object(sharepoint_client, '_make_graph_request') as mock_request:
            mock_response = Mock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_response_data)
            mock_request.return_value = mock_response
            
            drives = await sharepoint_client.list_drives("site1", "test-token")
            
            assert len(drives) == 2
            assert drives[0]["name"] == "Documents"
            assert drives[1]["name"] == "Shared Documents"
    
    @pytest.mark.asyncio
    async def test_get_file_metadata_success(self, sharepoint_client):
        """Test successful file metadata retrieval."""
        mock_metadata = {
            "id": "file1",
            "name": "document.pdf",
            "size": 2048,
            "createdDateTime": "2024-01-01T00:00:00Z",
            "lastModifiedDateTime": "2024-01-02T00:00:00Z",
            "createdBy": {
                "user": {
                    "displayName": "Test User"
                }
            }
        }
        
        with patch.object(sharepoint_client, '_make_graph_request') as mock_request:
            mock_response = Mock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_metadata)
            mock_request.return_value = mock_response
            
            metadata = await sharepoint_client.get_file_metadata(
                "drive1", "file1", "test-token"
            )
            
            assert metadata["name"] == "document.pdf"
            assert metadata["size"] == 2048
            assert metadata["createdBy"]["user"]["displayName"] == "Test User"
    
    @pytest.mark.asyncio
    async def test_network_error_handling(self, sharepoint_client):
        """Test handling of network errors."""
        with patch.object(sharepoint_client, '_make_graph_request') as mock_request:
            mock_request.side_effect = aiohttp.ClientError("Network error")
            
            with pytest.raises(Exception) as exc_info:
                await sharepoint_client.get_sites("test-token")
            
            assert "Network error" in str(exc_info.value)
    
    def test_sharepoint_client_initialization(self):
        """Test SharePoint client initialization."""
        client = SharePointClient(
            tenant_id="test-tenant",
            client_id="test-client", 
            client_secret="test-secret",
            site_name="test-site",
            library_name="Documents"
        )
        
        assert client.tenant_id == "test-tenant"
        assert client.client_id == "test-client"
        assert client.site_name == "test-site"
        assert client.library_name == "Documents"
