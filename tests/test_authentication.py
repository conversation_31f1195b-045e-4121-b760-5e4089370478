"""
Tests for authentication functionality.
"""

import pytest
import time
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

# Import authentication components to test
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from session_security import <PERSON><PERSON><PERSON><PERSON>
from authorization import <PERSON><PERSON><PERSON><PERSON><PERSON>
from csrf_protection import CSRFProtect


class TestSessionManager:
    """Test session management functionality."""
    
    @pytest.fixture
    def session_manager(self):
        """Create a session manager for testing."""
        return SessionManager(secret_key="test-secret-key")
    
    def test_create_microsoft_session_data(self, session_manager):
        """Test creation of Microsoft session data."""
        token_result = {
            "access_token": "test-access-token",
            "expires_in": 3600,
            "refresh_token": "test-refresh-token"
        }
        
        session_data = session_manager.create_microsoft_session_data(
            token_result, "<EMAIL>", "Test User"
        )
        
        assert session_data["authenticated"] == True
        assert session_data["auth_method"] == "microsoft"
        assert session_data["ms_user"]["email"] == "<EMAIL>"
        assert session_data["ms_user"]["name"] == "Test User"
        assert "auth_timestamp" in session_data
    
    def test_validate_session_microsoft_valid(self, session_manager):
        """Test validation of valid Microsoft session."""
        session = {
            "authenticated": True,
            "auth_method": "microsoft",
            "ms_user": {"email": "<EMAIL>", "name": "Test User"},
            "ms_token_cache": {
                "encrypted_access_token": "encrypted-token",
                "expires_on": time.time() + 3600  # Valid for 1 hour
            }
        }
        
        with patch.object(session_manager, 'decrypt_token', return_value="valid-token"):
            result = session_manager.validate_session(session)
            assert result == True
    
    def test_validate_session_microsoft_expired(self, session_manager):
        """Test validation of expired Microsoft session."""
        session = {
            "authenticated": True,
            "auth_method": "microsoft",
            "ms_user": {"email": "<EMAIL>", "name": "Test User"},
            "ms_token_cache": {
                "encrypted_access_token": "encrypted-token",
                "expires_on": time.time() - 3600  # Expired 1 hour ago
            }
        }
        
        result = session_manager.validate_session(session)
        assert result == False
    
    def test_validate_session_basic_auth_valid(self, session_manager):
        """Test validation of valid basic auth session."""
        session = {
            "authenticated": True,
            "auth_method": "basic",
            "basic_user": {
                "authenticated": True,
                "username": "admin"
            }
        }
        
        result = session_manager.validate_session(session)
        assert result == True
    
    def test_validate_session_basic_auth_invalid(self, session_manager):
        """Test validation of invalid basic auth session."""
        session = {
            "authenticated": True,
            "auth_method": "basic",
            "basic_user": {
                "authenticated": False,
                "username": "admin"
            }
        }
        
        result = session_manager.validate_session(session)
        assert result == False
    
    def test_validate_session_no_auth(self, session_manager):
        """Test validation of unauthenticated session."""
        session = {"authenticated": False}
        
        result = session_manager.validate_session(session)
        assert result == False
    
    def test_encrypt_decrypt_token(self, session_manager):
        """Test token encryption and decryption."""
        original_token = "test-access-token-12345"
        
        encrypted = session_manager.encrypt_token(original_token)
        assert encrypted != original_token
        assert len(encrypted) > len(original_token)
        
        decrypted = session_manager.decrypt_token(encrypted)
        assert decrypted == original_token


class TestAdminChecker:
    """Test admin authorization functionality."""
    
    @pytest.fixture
    def admin_checker(self):
        """Create an admin checker for testing."""
        with patch('authorization.settings') as mock_settings:
            mock_settings.ADMIN_EMAILS = "<EMAIL>,<EMAIL>"
            return AdminChecker()
    
    def test_admin_email_validation_valid(self, admin_checker):
        """Test admin email validation with valid emails."""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",  # Case insensitive
            "<EMAIL>"
        ]
        
        for email in valid_emails:
            result = admin_checker.is_admin_email(email)
            assert result == True
    
    def test_admin_email_validation_invalid(self, admin_checker):
        """Test admin email validation with invalid emails."""
        invalid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            ""
        ]
        
        for email in invalid_emails:
            result = admin_checker.is_admin_email(email)
            assert result == False
    
    def test_admin_cache_refresh(self, admin_checker):
        """Test that admin email cache refreshes properly."""
        # First call should populate cache
        result1 = admin_checker.is_admin_email("<EMAIL>")
        assert result1 == True
        
        # Simulate cache expiry by setting old timestamp
        admin_checker._cache_timestamp = time.time() - 400  # 400 seconds ago
        
        # Should refresh cache on next call
        with patch('authorization.settings') as mock_settings:
            mock_settings.ADMIN_EMAILS = "<EMAIL>"
            result2 = admin_checker.is_admin_email("<EMAIL>")
            assert result2 == False  # Old admin no longer valid
            
            result3 = admin_checker.is_admin_email("<EMAIL>")
            assert result3 == True  # New admin is valid


class TestCSRFProtection:
    """Test CSRF protection functionality."""
    
    @pytest.fixture
    def csrf_protect(self):
        """Create a CSRF protection instance for testing."""
        return CSRFProtect(secret_key="test-csrf-secret")
    
    def test_generate_token(self, csrf_protect):
        """Test CSRF token generation."""
        session_id = "test-session-123"
        
        token = csrf_protect.generate_token(session_id)
        
        assert token is not None
        assert len(token) > 0
        assert ":" in token  # Should contain separators
        
        # Token should be different each time due to timestamp
        token2 = csrf_protect.generate_token(session_id)
        assert token != token2
    
    def test_validate_token_valid(self, csrf_protect):
        """Test validation of valid CSRF token."""
        session_id = "test-session-123"
        
        token = csrf_protect.generate_token(session_id)
        result = csrf_protect.validate_token(token, session_id)
        
        assert result == True
    
    def test_validate_token_wrong_session(self, csrf_protect):
        """Test validation with wrong session ID."""
        session_id = "test-session-123"
        wrong_session_id = "different-session-456"
        
        token = csrf_protect.generate_token(session_id)
        result = csrf_protect.validate_token(token, wrong_session_id)
        
        assert result == False
    
    def test_validate_token_expired(self, csrf_protect):
        """Test validation of expired token."""
        # Create CSRF protect with very short expiry
        csrf_short = CSRFProtect(secret_key="test-secret", token_expiry=1)
        session_id = "test-session-123"
        
        token = csrf_short.generate_token(session_id)
        
        # Wait for token to expire
        time.sleep(2)
        
        result = csrf_short.validate_token(token, session_id)
        assert result == False
    
    def test_validate_token_malformed(self, csrf_protect):
        """Test validation of malformed tokens."""
        session_id = "test-session-123"
        
        malformed_tokens = [
            "invalid-token",
            "too:few:parts",
            "too:many:parts:here:extra",
            "",
            "session:notanumber:signature"
        ]
        
        for token in malformed_tokens:
            result = csrf_protect.validate_token(token, session_id)
            assert result == False
    
    def test_validate_token_invalid_signature(self, csrf_protect):
        """Test validation with invalid signature."""
        session_id = "test-session-123"
        timestamp = str(int(time.time()))
        
        # Create token with wrong signature
        invalid_token = f"{session_id}:{timestamp}:wrong-signature"
        
        result = csrf_protect.validate_token(invalid_token, session_id)
        assert result == False
