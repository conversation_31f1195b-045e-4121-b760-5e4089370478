"""
Tests for file processing functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

# Import the functions to test
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import add_document_to_index
from input_validation import FileValidator, ValidationError


class TestFileProcessing:
    """Test file processing functionality."""
    
    @pytest.mark.asyncio
    async def test_add_document_to_index_success(self, sample_text_file, mock_llama_index):
        """Test successful document indexing."""
        with patch('app.app.state') as mock_state, \
             patch('app.SimpleDirectoryReader') as mock_reader, \
             patch('app.safe_index_ops') as mock_safe_ops, \
             patch('app.persist_index') as mock_persist:
            
            # Setup mocks
            mock_state.index = mock_llama_index["index"]
            mock_doc = Mock()
            mock_doc.metadata = {"original": "metadata"}
            mock_doc.get_content.return_value = "Test content"
            
            mock_reader_instance = Mock()
            mock_reader_instance.load_data.return_value = [mock_doc]
            mock_reader.return_value = mock_reader_instance
            
            mock_safe_ops.safe_insert_nodes = AsyncMock()
            mock_persist.return_value = None
            
            # Test metadata
            metadata = {
                "file_name": "test.txt",
                "sharepoint_id": "test-id",
                "web_url": "https://test.com"
            }
            
            # Call function
            await add_document_to_index(str(sample_text_file), metadata)
            
            # Verify calls
            mock_reader.assert_called_once()
            mock_safe_ops.safe_insert_nodes.assert_called_once()
            mock_persist.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_add_document_to_index_file_not_found(self):
        """Test handling of file not found error."""
        with patch('app.app.state') as mock_state, \
             patch('app.SimpleDirectoryReader') as mock_reader:
            
            mock_state.index = Mock()
            mock_reader.side_effect = FileNotFoundError("File not found")
            
            metadata = {"file_name": "nonexistent.txt"}
            
            # Should not raise exception but handle gracefully
            await add_document_to_index("/nonexistent/file.txt", metadata)
    
    @pytest.mark.asyncio
    async def test_add_document_to_index_timestamp_error(self, sample_text_file):
        """Test handling of timestamp-related errors."""
        with patch('app.app.state') as mock_state, \
             patch('app.SimpleDirectoryReader') as mock_reader, \
             patch('app.safe_index_ops') as mock_safe_ops, \
             patch('app.persist_index') as mock_persist:
            
            mock_state.index = Mock()
            mock_reader.side_effect = AttributeError("'NoneType' object has no attribute 'timestamp'")
            
            mock_safe_ops.safe_insert_nodes = AsyncMock()
            mock_persist.return_value = None
            
            metadata = {"file_name": "test.txt"}
            
            # Should handle the error and create fallback document
            await add_document_to_index(str(sample_text_file), metadata)
            
            # Should still try to insert nodes (fallback document)
            mock_safe_ops.safe_insert_nodes.assert_called_once()
    
    def test_file_validator_valid_filename(self):
        """Test file validation with valid filename."""
        validator = FileValidator()
        
        valid_names = [
            "document.pdf",
            "test_file.docx",
            "image.png",
            "data.txt"
        ]
        
        for name in valid_names:
            result = validator.validate_filename(name)
            assert result == name
    
    def test_file_validator_invalid_filename(self):
        """Test file validation with invalid filename."""
        validator = FileValidator()
        
        invalid_names = [
            "document.exe",  # Invalid extension
            "con.txt",       # Reserved name
            "../../../etc/passwd",  # Path traversal
            "file with spaces.pdf",  # Spaces (should be handled)
            ""               # Empty name
        ]
        
        for name in invalid_names:
            if name == "file with spaces.pdf":
                # This should be valid after sanitization
                result = validator.validate_filename(name)
                assert result == "file with spaces.pdf"
            elif name == "":
                with pytest.raises(ValidationError):
                    validator.validate_filename(name)
            else:
                with pytest.raises(ValidationError):
                    validator.validate_filename(name)
    
    def test_file_validator_path_validation(self, temp_dir):
        """Test file path validation."""
        validator = FileValidator()
        
        # Valid path within base directory
        valid_path = temp_dir / "subdir" / "file.txt"
        result = validator.validate_file_path(str(valid_path), str(temp_dir))
        assert Path(result) == valid_path
        
        # Invalid path outside base directory
        invalid_path = temp_dir.parent / "outside" / "file.txt"
        with pytest.raises(ValidationError):
            validator.validate_file_path(str(invalid_path), str(temp_dir))
    
    @pytest.mark.asyncio
    async def test_document_processing_with_metadata(self, sample_text_file, mock_llama_index):
        """Test document processing preserves and combines metadata."""
        with patch('app.app.state') as mock_state, \
             patch('app.SimpleDirectoryReader') as mock_reader, \
             patch('app.safe_index_ops') as mock_safe_ops, \
             patch('app.persist_index') as mock_persist:
            
            mock_state.index = mock_llama_index["index"]
            
            # Mock document with existing metadata
            mock_doc = Mock()
            mock_doc.metadata = {"existing_key": "existing_value"}
            mock_doc.get_content.return_value = "Test content"
            
            mock_reader_instance = Mock()
            mock_reader_instance.load_data.return_value = [mock_doc]
            mock_reader.return_value = mock_reader_instance
            
            mock_safe_ops.safe_insert_nodes = AsyncMock()
            mock_persist.return_value = None
            
            # New metadata to add
            new_metadata = {
                "file_name": "test.txt",
                "sharepoint_id": "test-id",
                "new_key": "new_value"
            }
            
            await add_document_to_index(str(sample_text_file), new_metadata)
            
            # Verify that insert_nodes was called
            mock_safe_ops.safe_insert_nodes.assert_called_once()
            
            # Get the nodes that were inserted
            call_args = mock_safe_ops.safe_insert_nodes.call_args
            nodes = call_args[0][1]  # Second argument (first is index)
            
            # Verify metadata was combined
            node_metadata = nodes[0].metadata
            assert "existing_key" in node_metadata
            assert "new_key" in node_metadata
            assert "file_name" in node_metadata
            assert node_metadata["existing_key"] == "existing_value"
            assert node_metadata["new_key"] == "new_value"
