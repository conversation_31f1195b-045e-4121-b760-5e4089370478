"""
CSRF (Cross-Site Request Forgery) protection for the DDB SharePoint RAG application.
Provides token generation, validation, and middleware for secure POST requests.
"""

import logging
import time
import hmac
import hashlib
import secrets
from typing import Optional
from fastapi import Request, HTTPException, Depends
from fastapi.security.utils import get_authorization_scheme_param
from config import settings

logger = logging.getLogger(__name__)

class CSRFError(Exception):
    """Custom exception for CSRF-related errors."""
    pass

class CSRFProtection:
    """CSRF protection implementation with token generation and validation."""
    
    def __init__(self):
        # Use session secret key for CSRF token generation
        self.secret_key = settings.SESSION_SECRET_KEY.get_secret_value()
        self.token_expiry = 3600  # 1 hour
    
    def generate_token(self, session_id: str) -> str:
        """
        Generate a CSRF token for the given session.
        
        Args:
            session_id: Unique session identifier
            
        Returns:
            str: CSRF token
        """
        # Generate timestamp
        timestamp = str(int(time.time()))
        
        # Create payload with session ID and timestamp
        payload = f"{session_id}:{timestamp}"
        
        # Generate HMAC signature
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        # Combine payload and signature
        token = f"{payload}:{signature}"
        
        logger.debug(f"Generated CSRF token for session")
        return token
    
    def validate_token(self, token: str, session_id: str) -> bool:
        """
        Validate a CSRF token.
        
        Args:
            token: CSRF token to validate
            session_id: Session ID to validate against
            
        Returns:
            bool: True if token is valid
        """
        if not token or not session_id:
            logger.warning("CSRF validation failed: missing token or session ID")
            return False
        
        try:
            # Split token into components
            parts = token.split(':')
            if len(parts) != 3:
                logger.warning("CSRF validation failed: invalid token format")
                return False
            
            token_session_id, timestamp_str, signature = parts
            
            # Verify session ID matches
            if token_session_id != session_id:
                logger.warning("CSRF validation failed: session ID mismatch")
                return False
            
            # Verify timestamp is not expired
            try:
                timestamp = int(timestamp_str)
                current_time = int(time.time())
                if current_time - timestamp > self.token_expiry:
                    logger.warning("CSRF validation failed: token expired")
                    return False
            except ValueError:
                logger.warning("CSRF validation failed: invalid timestamp")
                return False
            
            # Verify signature
            payload = f"{token_session_id}:{timestamp_str}"
            expected_signature = hmac.new(
                self.secret_key.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            if not hmac.compare_digest(signature, expected_signature):
                logger.warning("CSRF validation failed: invalid signature")
                return False
            
            logger.debug("CSRF token validation successful")
            return True
            
        except Exception as e:
            logger.error(f"CSRF validation error: {e}")
            return False
    
    def get_session_id(self, request: Request) -> str:
        """
        Get or create a session ID for CSRF token generation.
        
        Args:
            request: FastAPI request object
            
        Returns:
            str: Session ID
        """
        session = request.session
        
        # Get existing session ID or create a new one
        session_id = session.get("csrf_session_id")
        if not session_id:
            session_id = secrets.token_urlsafe(32)
            session["csrf_session_id"] = session_id
            logger.debug("Created new CSRF session ID")
        
        return session_id
    
    def get_token_from_request(self, request: Request) -> Optional[str]:
        """
        Extract CSRF token from request headers or form data.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Optional[str]: CSRF token if found
        """
        # Check X-CSRF-Token header first
        csrf_token = request.headers.get("X-CSRF-Token")
        if csrf_token:
            return csrf_token
        
        # Check Authorization header (Bearer token format)
        authorization = request.headers.get("Authorization")
        if authorization:
            scheme, token = get_authorization_scheme_param(authorization)
            if scheme.lower() == "csrf":
                return token
        
        return None

# Global CSRF protection instance
csrf_protection = CSRFProtection()

def get_csrf_token(request: Request) -> str:
    """
    Dependency to get a CSRF token for the current session.
    
    Args:
        request: FastAPI request object
        
    Returns:
        str: CSRF token
    """
    session_id = csrf_protection.get_session_id(request)
    return csrf_protection.generate_token(session_id)

def validate_csrf_token(request: Request) -> bool:
    """
    Dependency to validate CSRF token for POST requests.
    
    Args:
        request: FastAPI request object
        
    Returns:
        bool: True if token is valid
        
    Raises:
        HTTPException: If CSRF validation fails
    """
    # Skip CSRF validation for safe methods
    if request.method in ("GET", "HEAD", "OPTIONS", "TRACE"):
        return True
    
    # Get session ID
    session_id = csrf_protection.get_session_id(request)
    
    # Get token from request
    csrf_token = csrf_protection.get_token_from_request(request)
    
    if not csrf_token:
        logger.warning(f"CSRF validation failed: no token provided for {request.method} {request.url.path}")
        raise HTTPException(
            status_code=403,
            detail="CSRF token missing. Include X-CSRF-Token header or Authorization: CSRF <token> header."
        )
    
    # Validate token
    if not csrf_protection.validate_token(csrf_token, session_id):
        logger.warning(f"CSRF validation failed: invalid token for {request.method} {request.url.path}")
        raise HTTPException(
            status_code=403,
            detail="CSRF token invalid or expired."
        )
    
    logger.debug(f"CSRF validation successful for {request.method} {request.url.path}")
    return True

# Dependency for CSRF protection
CSRFProtect = Depends(validate_csrf_token)

async def add_csrf_header_middleware(request: Request, call_next):
    """
    Middleware to add CSRF token to response headers for client-side usage.
    
    Args:
        request: FastAPI request object
        call_next: Next middleware/route handler
        
    Returns:
        Response with CSRF token header
    """
    response = await call_next(request)
    
    # Add CSRF token to response headers for AJAX requests
    if request.method == "GET":
        try:
            csrf_token = get_csrf_token(request)
            response.headers["X-CSRF-Token"] = csrf_token
        except Exception as e:
            logger.warning(f"Failed to add CSRF token to response: {e}")
    
    return response