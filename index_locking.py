"""
Thread-safe index access management for the DDB SharePoint RAG application.
Provides locks and context managers for safe concurrent access to LlamaIndex operations.
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, AsyncContextManager
from contextlib import asynccontextmanager
from dataclasses import dataclass
from llama_index.core.schema import TextNode
import threading

logger = logging.getLogger(__name__)

@dataclass
class IndexOperationStats:
    """Statistics for index operations."""
    read_operations: int = 0
    write_operations: int = 0
    lock_wait_time: float = 0.0
    last_operation_time: float = 0.0

class IndexLockManager:
    """
    Manages concurrent access to the LlamaIndex with read-write locks.
    Allows multiple concurrent reads but exclusive writes.
    """
    
    def __init__(self):
        """Initialize the index lock manager."""
        self._read_count = 0
        self._write_lock = asyncio.Lock()
        self._read_lock = asyncio.Lock()
        self._condition = asyncio.Condition()
        self._stats = IndexOperationStats()
        self._operation_history = []  # Track recent operations for debugging
        
        # Thread safety for sync operations
        self._thread_lock = threading.RLock()
        
    async def _acquire_read_lock(self) -> None:
        """Acquire read lock (multiple readers allowed)."""
        async with self._read_lock:
            self._read_count += 1
            if self._read_count == 1:
                # First reader needs to wait for any ongoing write
                await self._write_lock.acquire()
    
    async def _release_read_lock(self) -> None:
        """Release read lock."""
        async with self._read_lock:
            self._read_count -= 1
            if self._read_count == 0:
                # Last reader releases the write lock
                self._write_lock.release()
    
    @asynccontextmanager
    async def read_lock(self, operation_name: str = "read") -> AsyncContextManager[None]:
        """
        Context manager for read operations on the index.
        Multiple read operations can proceed concurrently.
        
        Args:
            operation_name: Name of the operation for logging/debugging
        """
        start_time = time.time()
        
        try:
            logger.debug(f"Acquiring read lock for operation: {operation_name}")
            await self._acquire_read_lock()
            
            wait_time = time.time() - start_time
            self._stats.lock_wait_time += wait_time
            self._stats.read_operations += 1
            self._stats.last_operation_time = time.time()
            
            # Track operation for debugging
            self._operation_history.append({
                'type': 'read',
                'operation': operation_name,
                'timestamp': time.time(),
                'wait_time': wait_time
            })
            
            # Keep only last 100 operations
            if len(self._operation_history) > 100:
                self._operation_history = self._operation_history[-100:]
            
            logger.debug(f"Read lock acquired for {operation_name} (waited {wait_time:.3f}s)")
            yield
            
        finally:
            await self._release_read_lock()
            logger.debug(f"Read lock released for {operation_name}")
    
    @asynccontextmanager
    async def write_lock(self, operation_name: str = "write") -> AsyncContextManager[None]:
        """
        Context manager for write operations on the index.
        Write operations are exclusive (no other reads or writes).
        
        Args:
            operation_name: Name of the operation for logging/debugging
        """
        start_time = time.time()
        
        try:
            logger.debug(f"Acquiring write lock for operation: {operation_name}")
            await self._write_lock.acquire()
            
            wait_time = time.time() - start_time
            self._stats.lock_wait_time += wait_time
            self._stats.write_operations += 1
            self._stats.last_operation_time = time.time()
            
            # Track operation for debugging
            self._operation_history.append({
                'type': 'write',
                'operation': operation_name,
                'timestamp': time.time(),
                'wait_time': wait_time
            })
            
            # Keep only last 100 operations
            if len(self._operation_history) > 100:
                self._operation_history = self._operation_history[-100:]
            
            logger.debug(f"Write lock acquired for {operation_name} (waited {wait_time:.3f}s)")
            yield
            
        finally:
            self._write_lock.release()
            logger.debug(f"Write lock released for {operation_name}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about index operations."""
        return {
            'read_operations': self._stats.read_operations,
            'write_operations': self._stats.write_operations,
            'total_operations': self._stats.read_operations + self._stats.write_operations,
            'average_wait_time': (
                self._stats.lock_wait_time / max(1, self._stats.read_operations + self._stats.write_operations)
            ),
            'last_operation_time': self._stats.last_operation_time,
            'recent_operations': self._operation_history[-10:] if self._operation_history else []
        }
    
    def reset_stats(self):
        """Reset operation statistics."""
        self._stats = IndexOperationStats()
        self._operation_history = []

class SafeIndexOperations:
    """
    Wrapper class providing thread-safe operations for LlamaIndex.
    All operations are protected with appropriate read/write locks.
    """
    
    def __init__(self, lock_manager: IndexLockManager):
        """Initialize with a lock manager."""
        self.lock_manager = lock_manager
    
    async def safe_query(self, index, query_engine, query: str) -> Any:
        """
        Safely execute a query against the index.
        
        Args:
            index: LlamaIndex instance
            query_engine: Query engine instance
            query: Query string
            
        Returns:
            Query result
        """
        async with self.lock_manager.read_lock("query"):
            # Run the query in a thread to avoid blocking
            result = await asyncio.to_thread(query_engine.query, query)
            return result
    
    async def safe_insert_nodes(self, index, nodes: List[TextNode]) -> None:
        """
        Safely insert nodes into the index.
        
        Args:
            index: LlamaIndex instance
            nodes: List of TextNode objects to insert
        """
        async with self.lock_manager.write_lock(f"insert_{len(nodes)}_nodes"):
            await asyncio.to_thread(index.insert_nodes, nodes)
    
    async def safe_delete_ref_doc(self, index, doc_id: str, delete_from_docstore: bool = True) -> None:
        """
        Safely delete a document from the index.
        
        Args:
            index: LlamaIndex instance
            doc_id: Document ID to delete
            delete_from_docstore: Whether to delete from docstore
        """
        async with self.lock_manager.write_lock(f"delete_doc_{doc_id}"):
            await asyncio.to_thread(index.delete_ref_doc, doc_id, delete_from_docstore=delete_from_docstore)
    
    async def safe_persist_index(self, index, persist_dir: str) -> None:
        """
        Safely persist the index to storage.
        
        Args:
            index: LlamaIndex instance
            persist_dir: Directory to persist to
        """
        async with self.lock_manager.write_lock("persist_index"):
            await asyncio.to_thread(
                index.storage_context.persist,
                persist_dir=persist_dir
            )
    
    async def safe_read_docstore(self, index) -> Dict[str, Any]:
        """
        Safely read from the index docstore.
        
        Args:
            index: LlamaIndex instance
            
        Returns:
            Dictionary of document metadata
        """
        async with self.lock_manager.read_lock("read_docstore"):
            documents = {}
            
            if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
                # Create a copy to avoid holding the lock too long
                docs_copy = dict(index.docstore.docs.items())
                
                for doc_id, doc_info in docs_copy.items():
                    metadata = doc_info.metadata or {}
                    documents[doc_id] = {
                        'metadata': metadata,
                        'doc_id': doc_id
                    }
            
            return documents
    
    async def safe_search_docstore(self, index, search_criteria: Dict[str, Any]) -> List[str]:
        """
        Safely search the docstore for documents matching criteria.
        
        Args:
            index: LlamaIndex instance
            search_criteria: Dictionary of metadata to match
            
        Returns:
            List of document IDs matching criteria
        """
        async with self.lock_manager.read_lock("search_docstore"):
            matching_docs = []
            
            if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
                for doc_id, doc_info in index.docstore.docs.items():
                    if doc_info.metadata:
                        # Check if all search criteria match
                        match = True
                        for key, value in search_criteria.items():
                            if doc_info.metadata.get(key) != value:
                                match = False
                                break
                        
                        if match:
                            matching_docs.append(doc_id)
            
            return matching_docs
    
    async def safe_bulk_delete(self, index, doc_ids: List[str]) -> int:
        """
        Safely delete multiple documents from the index.
        
        Args:
            index: LlamaIndex instance
            doc_ids: List of document IDs to delete
            
        Returns:
            Number of documents successfully deleted
        """
        async with self.lock_manager.write_lock(f"bulk_delete_{len(doc_ids)}_docs"):
            deleted_count = 0
            
            for doc_id in doc_ids:
                try:
                    await asyncio.to_thread(
                        index.delete_ref_doc,
                        doc_id,
                        delete_from_docstore=True
                    )
                    deleted_count += 1
                except Exception as e:
                    logger.warning(f"Failed to delete document {doc_id}: {e}")
            
            return deleted_count
    
    async def safe_create_empty_index(self, create_func) -> Any:
        """
        Safely create a new empty index.
        
        Args:
            create_func: Function that creates the index
            
        Returns:
            New index instance
        """
        async with self.lock_manager.write_lock("create_empty_index"):
            return await asyncio.to_thread(create_func)

# Global instances
index_lock_manager = IndexLockManager()
safe_index_ops = SafeIndexOperations(index_lock_manager)

# Convenience functions for common operations
async def with_read_lock(operation_name: str = "read"):
    """Get a read lock context manager."""
    return index_lock_manager.read_lock(operation_name)

async def with_write_lock(operation_name: str = "write"):
    """Get a write lock context manager."""
    return index_lock_manager.write_lock(operation_name)

def get_index_stats() -> Dict[str, Any]:
    """Get current index operation statistics."""
    return index_lock_manager.get_stats()

def reset_index_stats():
    """Reset index operation statistics."""
    index_lock_manager.reset_stats()

# Utility for logging index status
def log_index_status():
    """Log current index operation status for debugging."""
    stats = get_index_stats()
    logger.info(
        f"Index stats: {stats['total_operations']} total ops "
        f"({stats['read_operations']} reads, {stats['write_operations']} writes), "
        f"avg wait: {stats['average_wait_time']:.3f}s"
    )